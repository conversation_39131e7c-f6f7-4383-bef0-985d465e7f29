<?php
session_start();
require_once 'db_conn.php';
require 'vendor/autoload.php';

// Verify user is logged in and has registrar role
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'registrar') {
    header("Location: login.php");
    exit();
}

// Check if the connection is established
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Update the SQL query
$sql = "SELECT * FROM students 
        WHERE registration_status = 'registered' 
        ORDER BY registration_timestamp DESC";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>

<meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Cashier Dashboard - DBTI Online Registration">
    <link rel="stylesheet" href="index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>Registrar Dashboard - DBTI Online Registration</title>
    
    <meta charset="UTF-8">
    <title>Registered Students</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .student-table {
            width: 90%;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }

        /* Container for the table */
        .table-container {
            width: 98%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .student-table th, .student-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            padding: 12px; /* Add padding for table cells */
            text-align: left; /* Align text to the left in table cells */
            border: 1px solid #ccc; /* Add border to table cells */
        }
        
        .student-table th {
            background-color: #f4f4f4;
        }

        /* Add this to your existing styles */
        .page-title {
            text-align: center;
            margin: 30px 0;
            font-size: 2rem;
            color: #333;
            font-weight: bold;
        }

        .email-btn {
            background-color: #4CAF50;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .email-btn:hover {
            background-color: #45a049;
        }

        footer {
            background-color: #2d3748;
            color: #ecf0f1;
            padding: 40px 5%;
            text-align: center;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        footer a {
            color: #e74c3c;
            text-decoration: none;
            font-weight: bold;
        }

        footer a:hover {
            text-decoration: underline;
        }


        .search-container {
    width: 95%;
    max-width: 1400px;
    margin: 20px auto;
    padding: 0 20px;
}

.search-box {
    position: relative;
    max-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 12px 45px;
    border: none;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

@media screen and (max-width: 768px) {
    .search-container {
        padding: 0 15px;
    }
    
    .search-box {
        max-width: 100%;
    }
}

.filter-container {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
    align-items: center;
}

.filter-select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: white;
    min-width: 150px;
}

.export-options {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.export-btn {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dropdown-menu {
    display: none;
    position: absolute;
    background-color: #fff;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
    border-radius: 4px;
    padding: 5px 0;
}

.dropdown-menu a {
    color: black;
    padding: 8px 16px;
    text-decoration: none;
    display: block;
    transition: background-color 0.2s;
}

.dropdown-menu a:hover {
    background-color: #f1f1f1;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

    </style>
</head>
<body>
    <nav>
        <img src="img/logo.webp" alt="DBTI Logo" class="logo">
        <div class="heading">DBTI Online Registration</div>
        <div class="hamburger" onclick="toggleNavbar()">
            <div></div>
            <div></div>
            <div></div>
        </div>
        <ul id="navbar">
            <li><a href="index.php">Home</a></li>
            <li><a href="about.php">About</a></li>
            <li><a href="registrar_dashboard.php">Dashboard</a></li>
            <li><a href="logout.php">Logout</a></li>
        </ul>
    </nav>
<!-- Add this after the nav and before the table -->
<div class="search-container">
    <div class="search-box">
        <i class="fas fa-search search-icon"></i>
        <input type="text" id="searchTable" placeholder="Search students..." onkeyup="searchFunction()">
    </div>
    
    <div class="filter-container">
        <select id="techFilter" class="filter-select">
            <option value="">All Technologies</option>
            <option value="Automotive">Automotive</option>
            <option value="Electronics">Electronics</option>
            <option value="Information">Information Technology</option>
            <option value="Electrical">Electrical</option>
            <option value="Instrumentation">Instrumentation</option>
            <option value="Welding">Metal Welding & Fabrication</option>
            <option value="Fitting">Maintainance Fitting & Machining</option>
          
        </select>

        <select id="yearFilter" class="filter-select">
            <option value="">All Year Levels</option>
            <option value="Year 1">Year 1</option>
            <option value="Year 2">Year 2</option>
            <option value="Year 3">Year 3</option>
            <option value="Year 4">Year 4</option>
        </select>

        <select id="programFilter" class="filter-select">
            <option value="">All Programs</option>
            <option value="Bachelor in Technology">Bachelor in Technology</option>
            <option value="Bachelor In Education">Bachelor In Education</option>
            <option value="Diploma In Technology">Diploma In Technology</option>
        </select>

        <div class="export-options">
            <button class="export-btn" onclick="exportToPDF()">
                <i class="fas fa-file-pdf"></i> Export to PDF
            </button>
            
            <div class="dropdown">
                <button class="export-btn dropdown-toggle">
                    <i class="fas fa-file-excel"></i> Export to Excel 
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="dropdown-menu">
                    <a href="#" onclick="exportToExcel('class-list')">Class List Only</a>
                    <a href="#" onclick="exportToExcel('full-data')">Full Student Data</a>
                </div>
            </div>
        </div>
    </div>
</div>

    <br>

<div class="register">
            <h2 class="page-title">Registered Students</h2>
            

    <?php if ($result && $result->num_rows > 0): ?>
    <div class="table-container">
        <table class="student-table">
            <thead>
                <tr>
                    <th>Student ID</th>
                    <th>Name</th>
                    <th>Gender</th>
                    <th>Phone</th>
                    <th>Address</th>
                    <th>Province</th>
                    <th>Program</th>
                    <th>Technology</th>
                    <th>Year Level</th>
                    <th>Guardian Name</th>
                    <th>Guardian Phone</th>
                    <th>Student Email</th>
                    <th>DOB</th>
                    <th>Registration Date</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php while($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($row['student_id']); ?></td>
                    <td><?php echo htmlspecialchars($row['first_name'] . ' ' . $row['last_name']); ?></td>
                    <td><?php echo htmlspecialchars($row['gender']); ?></td>
                    <td><?php echo htmlspecialchars($row['phone_number']); ?></td>
                    <td><?php echo htmlspecialchars($row['residential_address']); ?></td>
                    <td><?php echo htmlspecialchars($row['home_province']); ?></td>
                    <td><?php echo htmlspecialchars($row['program']); ?></td>
                    <td><?php echo htmlspecialchars($row['tech']); ?></td>
                    <td><?php echo htmlspecialchars($row['year_level']); ?></td>
                    <td><?php echo htmlspecialchars($row['guardian_name']); ?></td>
                    <td><?php echo htmlspecialchars($row['guardian_phone_number']); ?></td>
                    <td><?php echo htmlspecialchars($row['student_email']); ?></td>
                    <td><?php echo htmlspecialchars($row['dob']); ?></td>
                    <td><?php echo !empty($row['registration_timestamp']) ? date('M d, Y g:i A', strtotime($row['registration_timestamp'])) : 'Not registered'; ?></td>
                    <td>
                        <button class="email-btn" onclick="sendEmail('<?php echo $row['student_email']; ?>', '<?php echo $row['student_id']; ?>')">
                            Send Email
                        </button>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
    <?php else: ?>
        <p>No students found.</p>
    <?php endif; ?>

    <script>
        function sendEmail(email, studentId) {
            // Create a modal popup overlay
            const overlay = document.createElement('div');
            overlay.style.cssText = 'position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:999;';
            
            // Create the iframe for the form
            const iframe = document.createElement('iframe');
            iframe.src = `send_course_outline.php?email=${email}&studentId=${studentId}`;
            iframe.style.cssText = 'position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); width:800px; height:600px; background:white; border:none; z-index:1000;';
            
            // Add close button
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = 'position:fixed; top:10px; right:10px; z-index:1001; font-size:24px; background:none; border:none; color:#fff; cursor:pointer;';
            closeBtn.onclick = () => {
                document.body.removeChild(overlay);
                document.body.removeChild(iframe);
                document.body.removeChild(closeBtn);
            };
            
            // Add elements to page
            document.body.appendChild(overlay);
            document.body.appendChild(iframe);
            document.body.appendChild(closeBtn);
        }
    </script>

    <footer>
        <div class="footer">
            <span>
                Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
                <a href="https://www.dbti.ac.pg/" target="_blank">
                    DBTI Website
                </a>
            </span>
        </div>
    </footer>
<style>
/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #f5f5f5;
    min-height: 100vh;
}

/* Navbar styles */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
}

nav .logo {
    height: 50px;
    width: auto;
    margin-right: 15px;
}

nav .heading {
    font-size: 1.75rem;
    font-weight: bold;
    color: #fff;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 20px;
}

nav ul li a {
    font-size: 1rem;
    color: #fff;
    text-transform: uppercase;
    padding: 12px 20px;
    background-color: #cc9900;
    border-radius: 6px;
    font-weight: 700;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
}

nav ul li a:hover {
    background-color: #996600;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger div {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 4px 0;
    transition: 0.4s;
}

@media only screen and (max-width: 768px) {
    nav {
        flex-wrap: wrap;
    }
    
    nav .logo {
        height: 40px;
    }
    
    nav ul {
        display: none;
        flex-direction: column;
        width: 100%;
        text-align: center;
        background-color: #ffbf00;
    }

    nav ul.active {
        display: flex;
    }

    nav ul li a {
        width: 200px;
        margin: 8px auto;
        text-align: center;
        display: block;
    }

    .hamburger {
        display: flex;
    }

    nav .heading {
        font-size: 1.2rem;
    }
}

/* Table styles */
.student-table {
    width: 95%;
    margin: 20px auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.student-table th {
    background: #4CAF50;
    color: white;
    padding: 15px;
    font-weight: 600;
}

.student-table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
}

.student-table tr:hover {
    background: #f8f9fa;
}

.email-btn {
    background: #4CAF50;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.email-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
}

/* Footer styles */
footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 15px 0;
    position: fixed;
    bottom: 0;
    width: 100%;
}

footer a {
    color: #ffbf00;
    text-decoration: none;
}

/* Mobile responsive */
@media screen and (max-width: 768px) {
    nav {
        flex-wrap: wrap;
    }
    
    nav .logo {
        height: 40px;
    }
    
    nav .heading {
        font-size: 1.2rem;
    }
    
    nav ul {
        display: none;
        flex-direction: column;
        width: 100%;
        background: #ffbf00;
    }
    
    nav ul.active {
        display: flex;
    }
    
    nav ul li a {
        width: 200px;
        margin: 8px auto;
        text-align: center;
    }
    
    .hamburger {
        display: flex;
    }
    
    .student-table {
        width: 100%;
        overflow-x: auto;
        display: block;
    }
    
    .student-table th, 
    .student-table td {
        min-width: 120px;
    }
}
</style>

<script>
function toggleNavbar() {
    const navbar = document.getElementById('navbar');
    navbar.classList.toggle('active');
}
</script>

<script>
function searchFunction() {
    let input = document.getElementById("searchTable");
    let filter = input.value.toUpperCase();
    let table = document.querySelector(".student-table");
    let tr = table.getElementsByTagName("tr");

    for (let i = 1; i < tr.length; i++) {
        let found = false;
        let td = tr[i].getElementsByTagName("td");
        for (let j = 0; j < td.length; j++) {
            if (td[j]) {
                let txtValue = td[j].textContent || td[j].innerText;
                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
        }
        tr[i].style.display = found ? "" : "none";
    }
}
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<script>
function applyFilters() {
    let tech = document.getElementById("techFilter").value.toUpperCase();
    let year = document.getElementById("yearFilter").value.toUpperCase();
    let program = document.getElementById("programFilter").value.toUpperCase();
    
    let table = document.querySelector(".student-table");
    let tr = table.getElementsByTagName("tr");

    for (let i = 1; i < tr.length; i++) {
        let td = tr[i].getElementsByTagName("td");
        let techMatch = !tech || td[7].textContent.toUpperCase().includes(tech);
        let yearMatch = !year || td[8].textContent.toUpperCase().includes(year);
        let programMatch = !program || td[6].textContent.toUpperCase().includes(program);
        
        tr[i].style.display = techMatch && yearMatch && programMatch ? "" : "none";
    }
}

function exportToPDF() {
    const doc = new jsPDF();
    const table = document.querySelector(".student-table");
    
    // Add title
    doc.setFontSize(16);
    doc.text("Student Class List", 14, 15);
    
    // Convert table to PDF
    doc.autoTable({ html: table });
    
    // Download PDF
    doc.save("class-list.pdf");
}

function exportToExcel(exportType) {
    const table = document.querySelector(".student-table");
    
    // Clone the table to avoid modifying the original
    const tempTable = table.cloneNode(true);
    const rows = tempTable.rows;
    
    // Define columns to exclude for each export type
    let excludeColumns = [];
    
    if (exportType === 'class-list') {
        // For class list, exclude these columns (using their indices)
        // Address, Province, Guardian Name, Guardian Phone, DOB, Registration Date, Action
        excludeColumns = [4, 5, 9, 10, 12, 13, 14]; // Adjust indices based on your table structure
    } else {
        // For full data, only exclude Action column
        excludeColumns = [14]; // Assuming Action is the last column
    }
    
    // Remove excluded columns from each row (starting from the last to avoid index shifting)
    for (let i = 0; i < rows.length; i++) {
        let cells = rows[i].cells;
        for (let j = excludeColumns.length - 1; j >= 0; j--) {
            if (cells.length > excludeColumns[j]) {
                cells[excludeColumns[j]].remove();
            }
        }
    }
    
    // Create a worksheet from the filtered table
    const wb = XLSX.utils.table_to_book(tempTable, {sheet: exportType === 'class-list' ? "Class List" : "Student Data"});
    
    // Generate filename based on export type
    const filename = exportType === 'class-list' ? "class-list.xlsx" : "full-student-data.xlsx";
    
    // Export to Excel
    XLSX.writeFile(wb, filename);
}

// Add event listeners to filters
document.getElementById("techFilter").addEventListener("change", applyFilters);
document.getElementById("yearFilter").addEventListener("change", applyFilters);
document.getElementById("programFilter").addEventListener("change", applyFilters);
</script>

<script>
function sortTable(n) {
    let table, rows, switching, i, x, y, shouldSwitch, dir, switchCount = 0;
    table = document.querySelector(".student-table");
    switching = true;
    dir = "asc";
    while (switching) {
        switching = false;
        rows = table.rows;
        for (i = 1; i < (rows.length - 1); i++) {
            shouldSwitch = false;
            x = rows[i].getElementsByTagName("td")[n];
            y = rows[i + 1].getElementsByTagName("td")[n];
            if (dir == "asc") {
                if (x.innerHTML.toLowerCase() > y.innerHTML.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                }
            } else {
                if (x.innerHTML.toLowerCase() < y.innerHTML.toLowerCase()) {
                    shouldSwitch = true;
                    break;
                }
            }
        }
        if (shouldSwitch) {
            rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
            switching = true;
            switchCount++;
        } else {
            if (switchCount == 0 && dir == "asc") {
                dir = "desc";
                switching = true;
            }
        }
    }
}
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropdownToggle = document.querySelector('.dropdown-toggle');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    
    // Toggle dropdown on click
    dropdownToggle.addEventListener('click', function(e) {
        e.preventDefault();
        dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!dropdownToggle.contains(e.target) && !dropdownMenu.contains(e.target)) {
            dropdownMenu.style.display = 'none';
        }
    });
});
</script>

</body>
</html>
