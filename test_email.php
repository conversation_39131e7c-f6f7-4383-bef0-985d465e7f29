<?php
// Basic test file to isolate email issues
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require 'vendor/autoload.php';
require 'PHPMailer-6.9.2/src/PHPMailer.php';
require 'PHPMailer-6.9.2/src/SMTP.php';
require 'PHPMailer-6.9.2/src/Exception.php';

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

echo "<h1>Email Test Page</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Testing basic email functionality...</p>";

// Test email configuration
$recipient = isset($_GET['email']) ? $_GET['email'] : '<EMAIL>';

// Get server details
echo "<h2>Server Information</h2>";
echo "<pre>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "\n";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "\n";
echo "</pre>";

// Function to test an email configuration
function testEmail($title, $useSMTP, $address) {
    echo "<h2>$title</h2>";
    echo "<pre>";
    
    try {
        $mail = new PHPMailer();
        
        if ($useSMTP) {
            $mail->SMTPDebug = SMTP::DEBUG_SERVER; // Enable verbose debug output
            $mail->isSMTP();
            $mail->Host       = 'smtp.hostinger.com';
            $mail->SMTPAuth   = true;
            $mail->Username   = '<EMAIL>';
            $mail->Password   = 'Blackpanther707@707';
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port       = 465;
        }
        
        // Set sender
        $mail->setFrom('<EMAIL>', 'DBTI Test Email');
        
        // Add recipient
        $mail->addAddress($address);
        
        // Email content
        $mail->isHTML(true);
        $mail->Subject = 'Test Email from DBTI - ' . date('Y-m-d H:i:s');
        $mail->Body    = 'This is a test email from the DBTI system at ' . date('Y-m-d H:i:s');
        
        // Send the email
        $result = $mail->send();
        echo "Mail sent: " . ($result ? "SUCCESS" : "FAILED") . "\n";
        
    } catch (Exception $e) {
        echo "Exception: " . $e->getMessage() . "\n";
    }
    
    echo "</pre>";
}

// Try direct PHP mail
testEmail("Testing with PHP mail()", false, $recipient);

// Try with SMTP
testEmail("Testing with SMTP", true, $recipient);

// Show phpinfo for mail configuration
echo "<h2>PHP Mail Configuration</h2>";
echo "<pre>";
$mailConfig = ini_get_all('mail');
print_r($mailConfig);
echo "</pre>";

// Check if file_exists works with normal paths
echo "<h2>File System Access</h2>";
echo "<pre>";
echo "Current directory: " . __DIR__ . "\n";
echo "Vendor directory exists: " . (file_exists('vendor') ? 'Yes' : 'No') . "\n";
echo "PHPMailer directory exists: " . (file_exists('PHPMailer-6.9.2') ? 'Yes' : 'No') . "\n";
echo "</pre>";

// Check if permissions are correct
echo "<h2>Permissions Check</h2>";
echo "<pre>";
echo "Temp directory: " . sys_get_temp_dir() . "\n";
echo "Is temp directory writable: " . (is_writable(sys_get_temp_dir()) ? 'Yes' : 'No') . "\n";
echo "</pre>";

echo "<p>Test completed.</p>";
?> 