<?php
// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration
require_once 'config.php';

// Session timeout check using the function from security_helpers.php
if (session_expired()) {
    session_unset();
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user is registrar
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'registrar') {
    header("Location: login.php");
    exit();
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Registrar Dashboard - DBTI Online Registration">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>Registrar Dashboard | DBTI Online</title>

    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            background-color: #f5f5f5;
            scroll-behavior: smooth;
        }

        /* Navbar */
        nav {
            background-color: #ffbf00;
            padding: 20px 5%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
        }

        nav .logo {
            height: 50px;
            width: auto;
            margin-right: 15px;
        }

        nav .heading {
            font-size: 1.75rem;
            font-weight: bold;
            color: #fff;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 20px;
        }

        nav ul li a {
            font-size: 1rem;
            color: #fff;
            text-transform: uppercase;
            padding: 12px 20px;
            background-color: #cc9900;
            border-radius: 6px;
            font-weight: 700;
            transition: all 0.3s ease;
            display: inline-block;
            text-decoration: none;
        }

        nav ul li a:hover {
            background-color: #996600;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 4px 0;
            transition: 0.4s;
        }

        /* Footer */
        footer {
            background-color: #333;
            color: white;
        /* Left Navigation Pane Styles */
        .sidebar {
            width: 250px;
            height: calc(100vh - 80px); /* Adjust based on your nav height */
            background: white;
            position: fixed;
            left: 0;
            top: 80px; /* Should match your nav height */
            padding: 20px;
            box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 100;
            transition: all 0.3s ease;
        }

        .main-content {
            margin-left: 250px;
            padding: 20px;
            min-height: calc(100vh - 80px);
        }

        .sidebar a {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            font-size: 1.2rem;
            font-weight: 700;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar a i {
            margin-right: 10px;
            font-size: 1.4rem;
        }

        .sidebar a:hover {
            background: #f0f0f0;
            transform: translateX(5px);
        }

        /* Main Content Adjustment */
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }

        /* Mobile Responsive */
        @media screen and (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                padding: 10px;
            }

            .sidebar a {
                text-align: center;
                justify-content: center;
            }

            .main-content {
                margin-left: 0;
                padding: 15px;
            }
        }            text-align: center;
            padding: 15px 0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        footer a {
            color: #ffbf00;
            text-decoration: none;
        }

        footer a:hover {
            text-decoration: underline;
        }

        /* Mobile Responsive */
        @media only screen and (max-width: 768px) {
            nav {
                flex-wrap: wrap;
            }

            nav .logo {
                height: 40px;
            }

            nav ul {
                display: none;
                flex-direction: column;
                width: 100%;
                text-align: center;
                background-color: #ffbf00;
            }

            nav ul.active {
                display: flex;
            }

            nav ul li a {
                width: 200px;
                margin: 8px auto;
                text-align: center;
                display: block;
            }

            .hamburger {
                display: flex;
            }

            nav .heading {
                font-size: 1.2rem;
            }
        }


        .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
}

@media screen and (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 550px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transform: scale(0.95);
    opacity: 0;
    animation: modalFadeIn 0.3s forwards;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.modal-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.6rem;
    display: flex;
    align-items: center;
}

.modal-header h2 i {
    margin-right: 12px;
    color: #ff6b00;
}

.modal-header .close {
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-header .close:hover {
    color: #333;
    transform: scale(1.1);
}

.modal-body {
    margin-bottom: 20px;
    line-height: 1.6;
    text-align: center;
}

.modal-body h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: #333;
}

.modal-body p {
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.modal-icon {
    font-size: 3rem;
    color: #ff6b00;
    margin-bottom: 15px;
}

.modal-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
    text-align: left;
}

.modal-info p {
    font-weight: 600;
    margin-bottom: 10px;
}

.modal-info ul {
    margin-left: 30px;
    margin-bottom: 0;
}

.modal-info ul li {
    margin-bottom: 8px;
}

.modal-warning {
    color: #e74c3c;
    font-size: 1.1rem;
}

.modal-footer {
    margin-top: 25px;
    display: flex;
    justify-content: center;
    gap: 15px;
}

.btn {
    padding: 12px 25px;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 1rem;
    min-width: 120px;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.cancel-btn {
    background-color: #f1f1f1;
    color: #333;
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

.reset-confirm {
    background-color: #ff6b00 !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(255, 107, 0, 0.3);
}

.reset-confirm:hover {
    background-color: #e05e00 !important;
    box-shadow: 0 6px 20px rgba(255, 107, 0, 0.4);
}

/* Notification Styles */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.5s forwards;
    background: white;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
}

.notification.success {
    border-left: 5px solid #4CAF50;
}

.notification.error {
    border-left: 5px solid #f44336;
}

.notification-icon {
    margin-right: 15px;
    font-size: 1.5rem;
}

.notification.success .notification-icon {
    color: #4CAF50;
}

.notification.error .notification-icon {
    color: #f44336;
}

.notification-message {
    flex: 1;
    font-size: 1rem;
    color: #333;
}

.notification-close {
    background: transparent;
    border: none;
    color: #aaa;
    cursor: pointer;
    font-size: 1rem;
    padding: 5px;
    transition: color 0.3s ease;
}

.notification-close:hover {
    color: #333;
}

.fade-out {
    animation: fadeOut 0.5s forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@media screen and (max-width: 480px) {
    #notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: calc(100% - 20px);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #333;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.action-button.reset {
    background-color: #f44336;
    color: white;
    font-weight: bold;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 15px;
}

.action-button.reset:hover {
    background-color: #d32f2f;
}

    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav>
        <img src="img/logo.webp" alt="DBTI Logo" class="logo">
        <div class="heading">DBTI Online Registration</div>
        <div class="hamburger" onclick="toggleNavbar()">
            <div></div>
            <div></div>
            <div></div>
        </div>
        <ul id="navbar">
            <li><a href="index.php">Home</a></li>
            <li><a href="about.php">About</a></li>
            <li><a href="registrar_dashboard.php">Dashboard</a></li>
            <li><a href="logout.php">Logout <i class="fa-solid fa-user"></i></a></li>
        </ul>
    </nav>

    <!-- Add this right after the nav section -->
    <div class="sidebar">
        <a href="../registered_students.php" class="stats-link">
            <i class="fa-solid fa-chart-simple"></i>
            <span>Stats</span>
        </a>
        <a href="manage_courses.php" class="stats-link">
            <i class="fa-solid fa-book"></i>
            <span>Manage Courses</span>
        </a>
        <button class="action-button reset" onclick="openResetModal()">
            <i class="fas fa-sync-alt"></i> Sem Reset
        </button>
        <a href="check_courses.php" class="action-button">
            <i class="fas fa-database"></i> Check Course Database
        </a>
    </div>

    <style>
    .sidebar {
        width: 280px;
        height: calc(100vh - 250px);
        background: white;
        position: fixed;
        left: 0;
        top: 80px;
        padding: 25px;
        box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);
        z-index: 100;
        margin-bottom: 60px;
        border-radius: 0 20px 20px 0;
        transition: all 0.3s ease;
    }

    .stats-link {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        color: #333;
        text-decoration: none;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
        margin-bottom: 10px;
    }

    .stats-link i {
        margin-right: 12px;
        font-size: 1.3rem;
        min-width: 25px;
        text-align: center;
        color: #666;
    }

    .stats-link:hover {
        background: #f8f8f8;
        transform: translateX(5px);
        color: #ff6b00;
    }

    .stats-link:hover i {
        color: #ff6b00;
    }

    .action-button {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        color: #333;
        text-decoration: none;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
        margin-bottom: 10px;
        background: transparent;
        border: none;
        width: 100%;
        text-align: left;
        cursor: pointer;
    }

    .action-button i {
        margin-right: 12px;
        font-size: 1.3rem;
        min-width: 25px;
        text-align: center;
    }

    .action-button.reset {
        background-color: #fff0e0;
        color: #ff6b00;
        border-left: 4px solid #ff6b00;
    }

    .action-button.reset:hover {
        background-color: #ffe0cc;
        transform: translateX(5px);
    }

    .main-content {
        margin-left: 280px;
        padding: 25px;
        transition: margin-left 0.3s ease;
    }

    /* Mobile Responsive Sidebar */
    @media screen and (max-width: 992px) {
        .sidebar {
            width: 250px;
        }

        .main-content {
            margin-left: 250px;
        }
    }

    @media screen and (max-width: 768px) {
        .sidebar {
            width: 100%;
            height: auto;
            position: relative;
            top: 0;
            padding: 15px;
            border-radius: 0;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        .stats-link, .action-button {
            width: auto;
            padding: 10px 15px;
            margin: 5px;
            font-size: 0.9rem;
        }

        .stats-link i, .action-button i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        .main-content {
            margin-left: 0;
            padding: 15px;
        }
    }

    @media screen and (max-width: 480px) {
        .sidebar {
            flex-direction: column;
            padding: 10px;
        }

        .stats-link, .action-button {
            width: 100%;
            margin: 3px 0;
        }
    }
    </style>
    <?php
    // Add this at the top of your file after session_start()
    require_once 'db_conn.php';

    // Count registered students and group by year level
    $sql = "SELECT year_level, COUNT(*) as total
            FROM students
            WHERE registration_status = 'registered'
            GROUP BY year_level";
    $result = $conn->query($sql);

    // Add database error handling
    if (!$result) {
        error_log("Database query failed: " . $conn->error);
        // Show user-friendly error message
        die("Unable to fetch statistics. Please try again later.");
    }

    $totalRegistered = 0;
    $total4thYear = 0;
    $total3rdYear = 0;
    $total2ndYear = 0;
    $total1stYear = 0;

    while ($row = $result->fetch_assoc()) {
        $totalRegistered += $row['total'];
        switch ($row['year_level']) {
            case 'Year 4':
                $total4thYear = $row['total'];
                break;
            case 'Year 3':
                $total3rdYear = $row['total'];
                break;
            case 'Year 2':
                $total2ndYear = $row['total'];
                break;
            case 'Year 1':
                $total1stYear = $row['total'];
                break;
        }
    }
    ?>
    <!-- Main Content -->
    <div class="main-content">
        <h1>Welcome Registrar!</h1>
        <div class="info-box">
            <h2><i class="fa-solid fa-user-graduate"></i> Registered Students</h2>
            <div class="stats-count">
                Total: <span class="number"><?php echo $totalRegistered; ?></span>
            </div>
        </div>
        <div class="stats-grid">
            <div class="info-box">
                <h2><i class="fa-solid fa-4"></i> th Year Students</h2>
                <div class="stats-count">
                    Total: <span class="number"><?php echo $total4thYear; ?></span>
                </div>
            </div>

            <div class="info-box">
                <h2><i class="fa-solid fa-3"></i> rd Year Students</h2>
                <div class="stats-count">
                    Total: <span class="number"><?php echo $total3rdYear; ?></span>
                </div>
            </div>

            <div class="info-box">
                <h2><i class="fa-solid fa-2"></i> nd Year Students</h2>
                <div class="stats-count">
                    Total: <span class="number"><?php echo $total2ndYear; ?></span>
                </div>
            </div>

            <div class="info-box">
                <h2><i class="fa-solid fa-1"></i> st Year Students</h2>
                <div class="stats-count">
                    Total: <span class="number"><?php echo $total1stYear; ?></span>
                </div>
            </div>
        </div>
        <div class="action-card">
            <h3>Registration Management</h3>
            <button id="resetRegistrationBtn" class="action-button reset-btn" onclick="openResetModal()">
                <i class="fas fa-sync-alt"></i> Reset Student Registrations
            </button>
            <p class="action-description"><i class="fas fa-info-circle"></i> Use this to reset student registration status at the end of a semester</p>
        </div>
    </div>

    <style>
    .stats-count {
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
        margin-top: 15px;
        text-align: center;
    }

    .stats-count .number {
        color: #4CAF50;
        font-size: 2rem;
    }

    .main-content h1 {
        text-align: center;
        margin-bottom: 30px;
        font-size: 2.2rem;
        color: #333;
    }

    .info-box {
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
        padding: 30px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .info-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .info-box h2 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: #333;
    }

    .action-card {
        text-align: center;
        max-width: 800px;
        margin: 30px auto;
        padding: 30px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #ffbf00;
        transition: all 0.3s ease;
    }

    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .action-card h3 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: #333;
        position: relative;
        display: inline-block;
        padding-bottom: 10px;
    }

    .action-card h3:after {
        content: '';
        position: absolute;
        width: 50%;
        height: 3px;
        background: #ffbf00;
        bottom: 0;
        left: 25%;
    }

    .action-button.reset-btn {
        background: linear-gradient(135deg, #ff6b00, #ff9500);
        color: white;
        border: none;
        padding: 15px 25px;
        border-radius: 50px;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 20px auto;
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 400px;
        box-shadow: 0 4px 15px rgba(255, 107, 0, 0.3);
        transition: all 0.3s ease;
    }

    .action-button.reset-btn:hover {
        background: linear-gradient(135deg, #e05e00, #e08600);
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(255, 107, 0, 0.4);
    }

    .action-button.reset-btn i {
        margin-right: 10px;
        font-size: 1.2rem;
    }

    .action-description {
        color: #666;
        font-size: 1rem;
        margin-top: 15px;
    }

    .action-description i {
        color: #ffbf00;
        margin-right: 5px;
    }
    </style>
    <!-- Footer -->
    <footer>
        <div class="footer">
            <span>
                Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
                <a href="https://www.dbti.ac.pg/" target="_blank">DBTI Website</a>
            </span>
        </div>
    </footer>

    <!-- Notification Container -->
    <div id="notification-container"></div>

    <script>
        function toggleNavbar() {
            const navbar = document.getElementById('navbar');
            navbar.classList.toggle('active');
        }

        function showStats() {
            window.location.href = '../registered_students.php';
        }

        function toggleStudentInfo() {
            const studentInfo = document.getElementById("studentInfo");
            studentInfo.style.display = studentInfo.style.display === "none" ? "block" : "none";
        }

        function openResetModal() {
            document.getElementById('resetModal').style.display = 'block';
        }

        function closeResetModal() {
            document.getElementById('resetModal').style.display = 'none';
        }

        // Notification system
        function showNotification(type, message) {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            // Icon based on type
            let icon = type === 'success' ? 'check-circle' : 'exclamation-circle';

            notification.innerHTML = `
                <div class="notification-icon">
                    <i class="fas fa-${icon}"></i>
                </div>
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    notification.remove();
                }, 500);
            }, 5000);
        }

        function resetRegistrations() {
            // Show loading indicator
            const resetBtn = document.querySelector('.reset-confirm');
            resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
            resetBtn.disabled = true;

            // Send AJAX request to reset registrations
            fetch('reset_registration.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                resetBtn.disabled = false;
                resetBtn.innerHTML = 'Reset';

                if (data.success) {
                    let message = data.message;
                    let count = data.count || 0;
                    if (count > 0) {
                        message = `Successfully reset registration status for ${count} student(s). They can now register for the new semester.`;
                    }

                    // Show success notification
                    showNotification('success', message);

                    // Refresh the page after 2 seconds to update the counts
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    // Show error notification
                    showNotification('error', 'Error: ' + data.message);
                }
                closeResetModal();
            })
            .catch(error => {
                resetBtn.disabled = false;
                resetBtn.innerHTML = 'Reset';
                showNotification('error', 'An error occurred. Please try again.');
                console.error('Error:', error);
                closeResetModal();
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('resetModal');
            if (event.target == modal) {
                closeResetModal();
            }
        }

        function resetRegistration() {
            // Use the modal instead of confirm dialog
            openResetModal();
        }
    </script>

    <!-- Add the modal HTML at the end of the body before the closing </body> tag -->
    <div id="resetModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-sync-alt"></i> Semester Reset</h2>
                <span class="close" onclick="closeResetModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="modal-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>End of Semester Reset</h3>
                <p>This action will reset the registration status of <strong>all currently registered students</strong> to "unregistered".</p>
                <div class="modal-info">
                    <p><i class="fas fa-info-circle"></i> This should only be done at the end of a semester when:</p>
                    <ul>
                        <li>All current semester activities are complete</li>
                        <li>You're ready for students to register for the new semester</li>
                    </ul>
                </div>
                <p class="modal-warning"><strong>Warning:</strong> This action cannot be undone. All students will need to register again for the new semester.</p>
            </div>
            <div class="modal-footer">
                <button class="btn cancel-btn" onclick="closeResetModal()">Cancel</button>
                <button class="btn reset-confirm" onclick="resetRegistrations()">Reset All Students</button>
            </div>
        </div>
    </div>
</body>
</html>
