<?php
session_start();
require_once 'db_conn.php';

// Enable detailed error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is admin or logged in student
$is_admin = (isset($_SESSION['role']) && $_SESSION['role'] === 'admin');
$is_student = (isset($_SESSION['role']) && $_SESSION['role'] === 'student');

if (!$is_admin && !$is_student) {
    header("Location: login.php");
    exit();
}

// Get the student ID to check
$student_id = '';
if ($is_student) {
    $student_id = $_SESSION['username'];
} elseif ($is_admin && isset($_GET['student_id'])) {
    $student_id = $_GET['student_id'];
}

// Page header
$page_title = $is_admin ? "Registration Debug Tool" : "My Registration Status";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        
        .section h3 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            color: #333;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        table, th, td {
            border: 1px solid #ddd;
        }
        
        th, td {
            padding: 10px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        .success {
            color: green;
        }
        
        .warning {
            color: orange;
        }
        
        .error {
            color: red;
        }
        
        .debug-info {
            font-family: monospace;
            white-space: pre-wrap;
            background: #f5f5f5;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 15px;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .action-buttons {
            margin-top: 20px;
        }
        
        .action-buttons a {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .action-buttons a.secondary {
            background-color: #6c757d;
        }
    </style>
</head>

<body>
    <!-- Navigation Bar -->
    <nav>
        <div class="logo">
            <img src="dbti.png" alt="DBTI Logo">
            <h2>Don Bosco Technological Institute</h2>
        </div>
        <ul>
            <?php if ($is_admin): ?>
                <li><a href="admin_dashboard.php">Dashboard</a></li>
                <li><a href="manage_courses.php">Manage Courses</a></li>
                <li><a href="manage_students.php">Manage Students</a></li>
                <li><a href="debug_registration.php" class="active">Debug Tool</a></li>
            <?php elseif ($is_student): ?>
                <li><a href="student_dashboard.php">Dashboard</a></li>
                <li><a href="registration.php">Registration</a></li>
                <li><a href="debug_registration.php" class="active">My Registration</a></li>
            <?php endif; ?>
            <li><a href="logout.php">Logout</a></li>
        </ul>
    </nav>
    
    <div class="debug-container">
        <h1><?php echo $page_title; ?></h1>
        
        <?php if ($is_admin && empty($student_id)): ?>
            <!-- Admin search form -->
            <div class="section">
                <h3>Search Student</h3>
                <form action="debug_registration.php" method="get">
                    <div class="form-group">
                        <label for="student_id">Student ID:</label>
                        <input type="text" id="student_id" name="student_id" required>
                    </div>
                    <div class="form-group">
                        <button type="submit">Check Registration Status</button>
                    </div>
                </form>
            </div>
            
            <!-- Database tables overview -->
            <div class="section">
                <h3>Database Tables Overview</h3>
                <?php
                // Check tables and record counts
                $tables = ['students', 'courses', 'registrations', 'course_registrations', 'payments'];
                
                echo "<table>";
                echo "<tr><th>Table Name</th><th>Exists</th><th>Record Count</th></tr>";
                
                foreach ($tables as $table) {
                    // Check if table exists
                    $check_table_sql = "SHOW TABLES LIKE '$table'";
                    $check_result = $conn->query($check_table_sql);
                    $table_exists = $check_result->num_rows > 0;
                    
                    // Get record count if table exists
                    $record_count = 0;
                    if ($table_exists) {
                        $count_sql = "SELECT COUNT(*) as count FROM $table";
                        $count_result = $conn->query($count_sql);
                        $record_count = $count_result->fetch_assoc()['count'];
                    }
                    
                    $status_class = $table_exists ? 'success' : 'error';
                    echo "<tr>";
                    echo "<td>$table</td>";
                    echo "<td class='$status_class'>" . ($table_exists ? 'Yes' : 'No') . "</td>";
                    echo "<td>" . ($table_exists ? $record_count : 'N/A') . "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
                ?>
            </div>
            
            <!-- System status -->
            <div class="section">
                <h3>System Status</h3>
                <?php
                // Check PHP version
                echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
                
                // Check MySQL version
                $version_sql = "SELECT VERSION() as version";
                $version_result = $conn->query($version_sql);
                $mysql_version = $version_result->fetch_assoc()['version'];
                echo "<p><strong>MySQL Version:</strong> " . $mysql_version . "</p>";
                
                // Check if error logging is enabled
                $error_reporting = error_reporting();
                $display_errors = ini_get('display_errors');
                $log_errors = ini_get('log_errors');
                $error_log = ini_get('error_log');
                
                echo "<p><strong>Error Reporting:</strong> " . ($error_reporting === E_ALL ? 'Full (E_ALL)' : 'Partial') . "</p>";
                echo "<p><strong>Display Errors:</strong> " . ($display_errors ? 'Enabled' : 'Disabled') . "</p>";
                echo "<p><strong>Log Errors:</strong> " . ($log_errors ? 'Enabled' : 'Disabled') . "</p>";
                echo "<p><strong>Error Log:</strong> " . ($error_log ? $error_log : 'Default') . "</p>";
                ?>
            </div>
        <?php elseif (!empty($student_id)): ?>
            <!-- Student registration information -->
            <div class="section">
                <h3>Student Information</h3>
                <?php
                // Get student details
                $student_sql = "SELECT * FROM students WHERE student_id = ?";
                $student_stmt = $conn->prepare($student_sql);
                $student_stmt->bind_param("s", $student_id);
                $student_stmt->execute();
                $student_result = $student_stmt->get_result();
                
                if ($student_result->num_rows > 0) {
                    $student = $student_result->fetch_assoc();
                    
                    echo "<table>";
                    echo "<tr><th>Field</th><th>Value</th></tr>";
                    echo "<tr><td>Student ID</td><td>" . htmlspecialchars($student['student_id']) . "</td></tr>";
                    echo "<tr><td>Name</td><td>" . htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) . "</td></tr>";
                    echo "<tr><td>Program</td><td>" . ($student['program'] ? htmlspecialchars($student['program']) : '<span class="warning">Not set</span>') . "</td></tr>";
                    echo "<tr><td>Technology</td><td>" . ($student['tech'] ? htmlspecialchars($student['tech']) : '<span class="warning">Not set</span>') . "</td></tr>";
                    echo "<tr><td>Year Level</td><td>" . ($student['year_level'] ? htmlspecialchars($student['year_level']) : '<span class="warning">Not set</span>') . "</td></tr>";
                    
                    $registration_status_class = 'warning';
                    if ($student['registration_status'] === 'registered') {
                        $registration_status_class = 'success';
                    } else if ($student['registration_status'] === 'pending') {
                        $registration_status_class = 'warning';
                    }
                    
                    echo "<tr><td>Registration Status</td><td class='" . $registration_status_class . "'>" . htmlspecialchars($student['registration_status']) . "</td></tr>";
                    echo "</table>";
                } else {
                    echo "<p class='error'>No student found with ID: " . htmlspecialchars($student_id) . "</p>";
                }
                ?>
            </div>
            
            <!-- Payment Information -->
            <div class="section">
                <h3>Payment Information</h3>
                <?php
                // Get payment information
                $payment_sql = "SELECT SUM(amount) as total_paid FROM payments WHERE student_id = ?";
                $payment_stmt = $conn->prepare($payment_sql);
                $payment_stmt->bind_param("s", $student_id);
                $payment_stmt->execute();
                $payment_result = $payment_stmt->get_result();
                $payment_data = $payment_result->fetch_assoc();
                $total_paid = $payment_data['total_paid'] ?? 0;
                $required_fee = 4000;
                $has_paid = ($total_paid >= $required_fee);
                
                echo "<table>";
                echo "<tr><th>Payment Status</th><th>Amount</th></tr>";
                echo "<tr><td>Required Fee</td><td>K" . number_format($required_fee, 2) . "</td></tr>";
                echo "<tr><td>Total Paid</td><td>K" . number_format($total_paid, 2) . "</td></tr>";
                $balance = $required_fee - $total_paid;
                $balance_class = $balance <= 0 ? 'success' : 'error';
                echo "<tr><td>Balance</td><td class='" . $balance_class . "'>K" . number_format($balance, 2) . "</td></tr>";
                echo "<tr><td>Payment Status</td><td class='" . ($has_paid ? 'success' : 'error') . "'>" . ($has_paid ? 'Completed' : 'Incomplete') . "</td></tr>";
                echo "</table>";
                
                // Show payment history
                $payment_history_sql = "SELECT * FROM payments WHERE student_id = ? ORDER BY payment_date DESC";
                $payment_history_stmt = $conn->prepare($payment_history_sql);
                $payment_history_stmt->bind_param("s", $student_id);
                $payment_history_stmt->execute();
                $payment_history_result = $payment_history_stmt->get_result();
                
                if ($payment_history_result->num_rows > 0) {
                    echo "<h4 style='margin-top:20px;'>Payment History</h4>";
                    echo "<table>";
                    echo "<tr><th>Date</th><th>Amount</th><th>Method</th><th>Receipt</th><th>Status</th></tr>";
                    
                    while ($payment = $payment_history_result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . date('Y-m-d', strtotime($payment['payment_date'])) . "</td>";
                        echo "<td>K" . number_format($payment['amount'], 2) . "</td>";
                        echo "<td>" . htmlspecialchars($payment['payment_method']) . "</td>";
                        echo "<td>" . htmlspecialchars($payment['receipt_number']) . "</td>";
                        echo "<td>" . htmlspecialchars($payment['payment_status']) . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</table>";
                } else {
                    echo "<p class='warning' style='margin-top:20px;'>No payment records found</p>";
                }
                ?>
            </div>
            
            <!-- Registration Information -->
            <div class="section">
                <h3>Registration Information</h3>
                <?php
                // Get registration details
                $reg_sql = "SELECT * FROM registrations WHERE student_id = ? ORDER BY registration_date DESC";
                $reg_stmt = $conn->prepare($reg_sql);
                $reg_stmt->bind_param("s", $student_id);
                $reg_stmt->execute();
                $reg_result = $reg_stmt->get_result();
                
                if ($reg_result->num_rows > 0) {
                    // Student has registration records
                    $registration = $reg_result->fetch_assoc();
                    
                    echo "<table>";
                    echo "<tr><th>Registration ID</th><th>Date</th><th>Semester</th><th>Academic Year</th><th>Status</th></tr>";
                    echo "<tr>";
                    echo "<td>" . $registration['registration_id'] . "</td>";
                    echo "<td>" . date('Y-m-d H:i', strtotime($registration['registration_date'])) . "</td>";
                    echo "<td>" . htmlspecialchars($registration['semester']) . "</td>";
                    echo "<td>" . htmlspecialchars($registration['academic_year']) . "</td>";
                    echo "<td>" . htmlspecialchars($registration['status']) . "</td>";
                    echo "</tr>";
                    echo "</table>";
                    
                    // Get registered courses
                    $courses_sql = "SELECT cr.*, c.course_name FROM course_registrations cr 
                                   LEFT JOIN courses c ON cr.course_id = c.course_id 
                                   WHERE cr.registration_id = ?";
                    $courses_stmt = $conn->prepare($courses_sql);
                    $courses_stmt->bind_param("i", $registration['registration_id']);
                    $courses_stmt->execute();
                    $courses_result = $courses_stmt->get_result();
                    
                    if ($courses_result->num_rows > 0) {
                        echo "<h4 style='margin-top:20px;'>Registered Courses</h4>";
                        echo "<table>";
                        echo "<tr><th>Course ID</th><th>Course Name</th></tr>";
                        
                        while ($course = $courses_result->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($course['course_id']) . "</td>";
                            echo "<td>" . htmlspecialchars($course['course_name'] ?? 'Unknown Course') . "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                    } else {
                        echo "<p class='warning' style='margin-top:20px;'>No courses registered for this registration</p>";
                    }
                } else {
                    echo "<p class='warning'>No registration records found</p>";
                }
                ?>
            </div>
            
            <!-- Debugging Information -->
            <?php if ($is_admin): ?>
            <div class="section">
                <h3>Technical Debugging Information</h3>
                <div class="debug-info">
<?php
echo "Student ID: " . $student_id . "\n\n";

// Check registration status in student table
$student_check_sql = "SELECT registration_status FROM students WHERE student_id = ?";
$student_check_stmt = $conn->prepare($student_check_sql);
$student_check_stmt->bind_param("s", $student_id);
$student_check_stmt->execute();
$student_check_result = $student_check_stmt->get_result();
$student_check_data = $student_check_result->fetch_assoc();

echo "Registration Status in students table: " . ($student_check_data['registration_status'] ?? 'Not found') . "\n\n";

// Check registrations table
$reg_check_sql = "SELECT COUNT(*) as count FROM registrations WHERE student_id = ?";
$reg_check_stmt = $conn->prepare($reg_check_sql);
$reg_check_stmt->bind_param("s", $student_id);
$reg_check_stmt->execute();
$reg_check_result = $reg_check_stmt->get_result();
$reg_check_data = $reg_check_result->fetch_assoc();

echo "Number of registrations: " . $reg_check_data['count'] . "\n\n";

// Check course_registrations table
if ($reg_check_data['count'] > 0) {
    $reg_id_sql = "SELECT registration_id FROM registrations WHERE student_id = ? ORDER BY registration_date DESC LIMIT 1";
    $reg_id_stmt = $conn->prepare($reg_id_sql);
    $reg_id_stmt->bind_param("s", $student_id);
    $reg_id_stmt->execute();
    $reg_id_result = $reg_id_stmt->get_result();
    $reg_id_data = $reg_id_result->fetch_assoc();
    $reg_id = $reg_id_data['registration_id'];
    
    $course_reg_sql = "SELECT COUNT(*) as count FROM course_registrations WHERE registration_id = ?";
    $course_reg_stmt = $conn->prepare($course_reg_sql);
    $course_reg_stmt->bind_param("i", $reg_id);
    $course_reg_stmt->execute();
    $course_reg_result = $course_reg_stmt->get_result();
    $course_reg_data = $course_reg_result->fetch_assoc();
    
    echo "Number of registered courses: " . $course_reg_data['count'] . "\n";
}

// Check payment requirements
echo "\nPayment Requirements Check:\n";
echo "Total amount paid: K" . number_format($total_paid, 2) . "\n";
echo "Required fee: K" . number_format($required_fee, 2) . "\n";
echo "Payment status: " . ($has_paid ? "Sufficient" : "Insufficient") . "\n";

?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                <?php if ($is_admin): ?>
                    <a href="manage_students.php" class="secondary">Back to Student Management</a>
                    <a href="fix_registration.php?student_id=<?php echo $student_id; ?>">Fix Registration</a>
                <?php else: ?>
                    <a href="student_dashboard.php" class="secondary">Back to Dashboard</a>
                    <?php if (isset($student) && $student['registration_status'] !== 'registered'): ?>
                        <a href="registration.php">Start/Continue Registration</a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <footer>
        <p>&copy; <?php echo date("Y"); ?> Don Bosco Technological Institute. All Rights Reserved.</p>
    </footer>
</body>
</html> 