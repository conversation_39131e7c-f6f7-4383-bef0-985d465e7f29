<?php
require 'vendor/autoload.php';
require '../db_conn.php'; // Your database connection file

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log all webhook requests with timestamp
$log_file = $logs_dir . 'stripe_webhook.log';
file_put_contents($log_file,
    date('Y-m-d H:i:s') . ' - Webhook request received' . PHP_EOL,
    FILE_APPEND);

// Log request headers for debugging
$headers = getallheaders();
$headers_str = json_encode($headers, JSON_PRETTY_PRINT);
file_put_contents($log_file,
    date('Y-m-d H:i:s') . ' - Headers: ' . $headers_str . PHP_EOL,
    FILE_APPEND);

\Stripe\Stripe::setApiKey('sk_test_51OpK5NKnyip3i5w1YlQZg2XjdXWjIQ0UdTIXZHtkY1MoipKjhvMPurDOpSnf2v6fODGX1P5G9LWUSdafTr0rWsXy00U04R8JIH');

$endpoint_secret = 'whsec_9eOmdqyyzJxUcaxFXRpM7JRqAxF4cLaw';

$payload = @file_get_contents('php://input');
// Log the raw payload
file_put_contents($log_file,
    date('Y-m-d H:i:s') . ' - Payload: ' . $payload . PHP_EOL,
    FILE_APPEND);

$sig_header = isset($_SERVER['HTTP_STRIPE_SIGNATURE']) ? $_SERVER['HTTP_STRIPE_SIGNATURE'] : '';
// Log the signature header
file_put_contents($log_file,
    date('Y-m-d H:i:s') . ' - Signature: ' . $sig_header . PHP_EOL,
    FILE_APPEND);

$event = null;

try {
    $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
    // Log successful event construction
    file_put_contents($log_file,
        date('Y-m-d H:i:s') . ' - Event constructed successfully: ' . $event->type . PHP_EOL,
        FILE_APPEND);
} catch (\UnexpectedValueException $e) {
    // Invalid payload
    $error_message = "Invalid payload: " . $e->getMessage();
    http_response_code(400);
    error_log($error_message);

    // Log the error to our custom log file
    file_put_contents($log_file,
        date('Y-m-d H:i:s') . ' - ERROR: ' . $error_message . PHP_EOL,
        FILE_APPEND);
    exit();
} catch (\Stripe\Exception\SignatureVerificationException $e) {
    // Invalid signature
    $error_message = "Invalid signature: " . $e->getMessage();
    http_response_code(400);
    error_log($error_message);

    // Log the error to our custom log file
    file_put_contents($log_file,
        date('Y-m-d H:i:s') . ' - ERROR: ' . $error_message . PHP_EOL,
        FILE_APPEND);
    exit();
} catch (\Exception $e) {
    // Catch any other exceptions
    $error_message = "Unexpected error: " . $e->getMessage();
    http_response_code(500);
    error_log($error_message);

    // Log the error to our custom log file
    file_put_contents($log_file,
        date('Y-m-d H:i:s') . ' - ERROR: ' . $error_message . PHP_EOL,
        FILE_APPEND);
    exit();
}

// Log the event type
file_put_contents($log_file,
    date('Y-m-d H:i:s') . ' - Processing event: ' . $event->type . PHP_EOL,
    FILE_APPEND);

// Handle the checkout.session.completed event
if ($event->type === 'checkout.session.completed') {
    $session = $event->data->object;

    // Get details from the session
    $student_id = $session->client_reference_id;
    $transaction_id = $session->payment_intent;
    $amount = $session->amount_total / 100; // Convert to dollars

    // Log the payment details
    file_put_contents($log_file,
        date('Y-m-d H:i:s') . ' - Payment details: Student ID: ' . $student_id .
        ', Amount: ' . $amount .
        ', Transaction ID: ' . $transaction_id . PHP_EOL,
        FILE_APPEND);

    try {
        // Start transaction for database operations
        $conn->begin_transaction();

        // Insert payment into database
        $stmt = $conn->prepare("INSERT INTO payments (student_id, amount, payment_date, payment_method, transaction_id) VALUES (?, ?, NOW(), 'credit_card', ?)");
        if (!$stmt) {
            throw new Exception("Prepare failed: (" . $conn->errno . ") " . $conn->error);
        }

        $stmt->bind_param("sds", $student_id, $amount, $transaction_id);

        if (!$stmt->execute()) {
            throw new Exception("Error recording payment: (" . $stmt->errno . ") " . $stmt->error);
        }

        file_put_contents($log_file,
            date('Y-m-d H:i:s') . ' - Payment record inserted successfully' . PHP_EOL,
            FILE_APPEND);

        // Update student's payment status
        $update_stmt = $conn->prepare("UPDATE students SET payment_status = 'paid' WHERE student_id = ?");
        if (!$update_stmt) {
            throw new Exception("Update prepare failed: (" . $conn->errno . ") " . $conn->error);
        }

        $update_stmt->bind_param("s", $student_id);

        if (!$update_stmt->execute()) {
            throw new Exception("Error updating student status: (" . $update_stmt->errno . ") " . $update_stmt->error);
        }

        file_put_contents($log_file,
            date('Y-m-d H:i:s') . ' - Student payment status updated successfully' . PHP_EOL,
            FILE_APPEND);

        // Commit the transaction
        $conn->commit();

        file_put_contents($log_file,
            date('Y-m-d H:i:s') . ' - Transaction committed successfully' . PHP_EOL,
            FILE_APPEND);

        // Close statements
        $stmt->close();
        $update_stmt->close();

    } catch (Exception $e) {
        // Rollback the transaction on error
        $conn->rollback();

        $error_message = "Database error: " . $e->getMessage();
        error_log($error_message);

        file_put_contents($log_file,
            date('Y-m-d H:i:s') . ' - ERROR: ' . $error_message . PHP_EOL,
            FILE_APPEND);
    }
} else {
    // Log unhandled event type
    file_put_contents($log_file,
        date('Y-m-d H:i:s') . ' - Unhandled event type: ' . $event->type . PHP_EOL,
        FILE_APPEND);
}

// Log successful completion
file_put_contents($log_file,
    date('Y-m-d H:i:s') . ' - Webhook processing completed successfully' . PHP_EOL . PHP_EOL,
    FILE_APPEND);

http_response_code(200); // Acknowledge receipt of the event
?>
