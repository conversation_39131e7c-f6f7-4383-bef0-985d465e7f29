<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once __DIR__ . '/../config.php';
require '../db_conn.php';

// Check if user is admin or cashier
if (!isset($_SESSION['role']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'cashier')) {
    header("Location: ../login.php");
    exit();
}

// Initialize variables
$database_info = [];
$error = '';

try {
    // Get database name
    $db_name_result = $conn->query("SELECT DATABASE() as db_name");
    if ($db_name_result) {
        $db_row = $db_name_result->fetch_assoc();
        $database_info['name'] = $db_row['db_name'];
    }
    
    // Get all tables
    $tables_result = $conn->query("SHOW TABLES");
    $tables = [];
    
    if ($tables_result) {
        while ($row = $tables_result->fetch_array()) {
            $table_name = $row[0];
            $tables[] = $table_name;
            
            // Get table structure
            $structure_result = $conn->query("DESCRIBE `$table_name`");
            $columns = [];
            
            if ($structure_result) {
                while ($col_row = $structure_result->fetch_assoc()) {
                    $columns[] = [
                        'field' => $col_row['Field'],
                        'type' => $col_row['Type'],
                        'null' => $col_row['Null'],
                        'key' => $col_row['Key'],
                        'default' => $col_row['Default'],
                        'extra' => $col_row['Extra']
                    ];
                }
            }
            
            $database_info['tables'][$table_name] = [
                'columns' => $columns,
                'row_count' => 0
            ];
            
            // Get row count
            $count_result = $conn->query("SELECT COUNT(*) as count FROM `$table_name`");
            if ($count_result) {
                $count_row = $count_result->fetch_assoc();
                $database_info['tables'][$table_name]['row_count'] = $count_row['count'];
            }
        }
    }
    
    // Check for specific tables we need
    $required_tables = ['students', 'payments'];
    $database_info['missing_tables'] = [];
    
    foreach ($required_tables as $table) {
        if (!in_array($table, $tables)) {
            $database_info['missing_tables'][] = $table;
        }
    }
    
    // Check for required columns in students table
    if (in_array('students', $tables)) {
        $student_columns = array_column($database_info['tables']['students']['columns'], 'field');
        $required_student_columns = ['student_id', 'payment_status'];
        $database_info['missing_student_columns'] = [];
        
        foreach ($required_student_columns as $col) {
            if (!in_array($col, $student_columns)) {
                $database_info['missing_student_columns'][] = $col;
            }
        }
    }
    
    // Check for required columns in payments table
    if (in_array('payments', $tables)) {
        $payment_columns = array_column($database_info['tables']['payments']['columns'], 'field');
        $required_payment_columns = ['student_id', 'amount', 'transaction_id'];
        $database_info['missing_payment_columns'] = [];
        
        foreach ($required_payment_columns as $col) {
            if (!in_array($col, $payment_columns)) {
                $database_info['missing_payment_columns'][] = $col;
            }
        }
    }
    
} catch (Exception $e) {
    $error = "Error checking database: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Structure Check</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .table-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .back-link {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #666;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .status-icon {
            margin-right: 8px;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-database"></i> Database Structure Check</h1>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
            </div>
        <?php else: ?>
            
            <div class="table-info">
                <h2>Database: <?php echo htmlspecialchars($database_info['name'] ?? 'Unknown'); ?></h2>
                <p>Total Tables: <?php echo count($database_info['tables'] ?? []); ?></p>
            </div>
            
            <?php if (!empty($database_info['missing_tables'])): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle status-icon"></i>
                    <strong>Missing Required Tables:</strong> <?php echo implode(', ', $database_info['missing_tables']); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($database_info['missing_student_columns'])): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle status-icon"></i>
                    <strong>Missing Columns in 'students' table:</strong> <?php echo implode(', ', $database_info['missing_student_columns']); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($database_info['missing_payment_columns'])): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle status-icon"></i>
                    <strong>Missing Columns in 'payments' table:</strong> <?php echo implode(', ', $database_info['missing_payment_columns']); ?>
                </div>
            <?php endif; ?>
            
            <?php if (empty($database_info['missing_tables']) && empty($database_info['missing_student_columns']) && empty($database_info['missing_payment_columns'])): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle status-icon"></i>
                    All required tables and columns are present!
                </div>
            <?php endif; ?>
            
            <?php foreach ($database_info['tables'] ?? [] as $table_name => $table_info): ?>
                <h3>
                    <?php if (in_array($table_name, ['students', 'payments'])): ?>
                        <i class="fas fa-check-circle success status-icon"></i>
                    <?php else: ?>
                        <i class="fas fa-table status-icon"></i>
                    <?php endif; ?>
                    Table: <?php echo htmlspecialchars($table_name); ?> 
                    <small>(<?php echo $table_info['row_count']; ?> rows)</small>
                </h3>
                
                <table>
                    <thead>
                        <tr>
                            <th>Column</th>
                            <th>Type</th>
                            <th>Null</th>
                            <th>Key</th>
                            <th>Default</th>
                            <th>Extra</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($table_info['columns'] as $column): ?>
                            <tr>
                                <td>
                                    <?php 
                                    $is_required = false;
                                    if ($table_name === 'students' && in_array($column['field'], ['student_id', 'payment_status'])) {
                                        $is_required = true;
                                    } elseif ($table_name === 'payments' && in_array($column['field'], ['student_id', 'amount', 'transaction_id'])) {
                                        $is_required = true;
                                    }
                                    
                                    if ($is_required): ?>
                                        <i class="fas fa-star warning status-icon" title="Required for webhook"></i>
                                    <?php endif; ?>
                                    <?php echo htmlspecialchars($column['field']); ?>
                                </td>
                                <td><?php echo htmlspecialchars($column['type']); ?></td>
                                <td><?php echo htmlspecialchars($column['null']); ?></td>
                                <td><?php echo htmlspecialchars($column['key']); ?></td>
                                <td><?php echo htmlspecialchars($column['default'] ?? 'NULL'); ?></td>
                                <td><?php echo htmlspecialchars($column['extra']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endforeach; ?>
            
            <?php if (!empty($database_info['missing_tables']) || !empty($database_info['missing_student_columns']) || !empty($database_info['missing_payment_columns'])): ?>
                <h3>Recommended SQL Commands</h3>
                
                <?php if (in_array('payments', $database_info['missing_tables'] ?? [])): ?>
                    <h4>Create payments table:</h4>
                    <div class="code">
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    payment_method VARCHAR(50) DEFAULT 'credit_card',
    transaction_id VARCHAR(100) UNIQUE,
    INDEX idx_student_id (student_id),
    INDEX idx_transaction_id (transaction_id)
);
                    </div>
                <?php endif; ?>
                
                <?php if (in_array('payment_status', $database_info['missing_student_columns'] ?? [])): ?>
                    <h4>Add payment_status column to students table:</h4>
                    <div class="code">
ALTER TABLE students ADD COLUMN payment_status VARCHAR(20) DEFAULT 'unpaid';
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($database_info['missing_payment_columns'])): ?>
                    <h4>Add missing columns to payments table:</h4>
                    <?php foreach ($database_info['missing_payment_columns'] as $col): ?>
                        <div class="code">
                            <?php if ($col === 'student_id'): ?>
ALTER TABLE payments ADD COLUMN student_id VARCHAR(50) NOT NULL;
                            <?php elseif ($col === 'amount'): ?>
ALTER TABLE payments ADD COLUMN amount DECIMAL(10,2) NOT NULL;
                            <?php elseif ($col === 'transaction_id'): ?>
ALTER TABLE payments ADD COLUMN transaction_id VARCHAR(100) UNIQUE;
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            <?php endif; ?>
            
        <?php endif; ?>
        
        <a href="../cashier_dashboard.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</body>
</html>
