<?php
// This is a simplified webhook handler for debugging purposes

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Create logs directory if it doesn't exist
$logs_dir = __DIR__ . '/../logs/';
if (!file_exists($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

// Log file path
$log_file = $logs_dir . 'stripe_webhook_debug.log';

// Function to log messages
function log_message($message) {
    global $log_file;
    file_put_contents($log_file,
        date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL,
        FILE_APPEND);
}

// Start logging
log_message('Debug webhook handler started');

// Log PHP version and loaded extensions
log_message('PHP Version: ' . phpversion());
log_message('Loaded Extensions: ' . implode(', ', get_loaded_extensions()));

// Check if required files exist
log_message('Checking for required files...');

$vendor_autoload = __DIR__ . '/vendor/autoload.php';
$db_conn = __DIR__ . '/../db_conn.php';

if (file_exists($vendor_autoload)) {
    log_message('vendor/autoload.php exists');
} else {
    log_message('ERROR: vendor/autoload.php does not exist');
}

if (file_exists($db_conn)) {
    log_message('db_conn.php exists');
} else {
    log_message('ERROR: db_conn.php does not exist');
}

// Try to include required files
log_message('Attempting to include required files...');

try {
    // Include Stripe library
    if (file_exists($vendor_autoload)) {
        require $vendor_autoload;
        log_message('Successfully included vendor/autoload.php');
    } else {
        throw new Exception('Could not find vendor/autoload.php');
    }

    // Include database connection
    if (file_exists($db_conn)) {
        try {
            log_message('Attempting to include db_conn.php...');

            // Include with output buffering to catch any errors or output
            ob_start();
            $db_include_result = include $db_conn;
            $db_output = ob_get_clean();

            if ($db_include_result === false) {
                log_message('ERROR: Failed to include db_conn.php');
                if (!empty($db_output)) {
                    log_message('Output from db_conn.php: ' . $db_output);
                }
            } else {
                log_message('Successfully included db_conn.php');

                // Log any output from the file
                if (!empty($db_output)) {
                    log_message('Output from db_conn.php: ' . $db_output);
                }

                // Check if $conn variable exists
                if (isset($conn)) {
                    log_message('Database connection variable exists');

                    // Get connection info
                    $conn_info = [
                        'host_info' => $conn->host_info ?? 'unknown',
                        'server_info' => $conn->server_info ?? 'unknown',
                        'client_info' => $conn->client_info ?? 'unknown'
                    ];
                    log_message('Connection info: ' . json_encode($conn_info));

                    // Test database connection
                    try {
                        if ($conn->ping()) {
                            log_message('Database connection is working');

                            // Try a simple query
                            $result = $conn->query("SELECT 1");
                            if ($result) {
                                log_message('Simple query successful');

                                // Check if students table exists
                                $result = $conn->query("SHOW TABLES LIKE 'students'");
                                if ($result && $result->num_rows > 0) {
                                    log_message('Students table exists');

                                    // Check students table structure
                                    $result = $conn->query("DESCRIBE students");
                                    if ($result) {
                                        $columns = [];
                                        while ($row = $result->fetch_assoc()) {
                                            $columns[] = $row['Field'];
                                        }
                                        log_message('Students table columns: ' . implode(', ', $columns));

                                        // Check if payment_status column exists
                                        if (in_array('payment_status', $columns)) {
                                            log_message('payment_status column exists in students table');
                                        } else {
                                            log_message('ERROR: payment_status column does not exist in students table');
                                        }
                                    }
                                } else {
                                    log_message('ERROR: Students table does not exist');
                                }

                                // Check if payments table exists
                                $result = $conn->query("SHOW TABLES LIKE 'payments'");
                                if ($result && $result->num_rows > 0) {
                                    log_message('Payments table exists');

                                    // Check payments table structure
                                    $result = $conn->query("DESCRIBE payments");
                                    if ($result) {
                                        $columns = [];
                                        while ($row = $result->fetch_assoc()) {
                                            $columns[] = $row['Field'];
                                        }
                                        log_message('Payments table columns: ' . implode(', ', $columns));
                                    }
                                } else {
                                    log_message('ERROR: Payments table does not exist');
                                }
                            } else {
                                log_message('ERROR: Simple query failed: ' . $conn->error);
                            }
                        } else {
                            log_message('ERROR: Database connection failed: ' . $conn->error);
                        }
                    } catch (Exception $e) {
                        log_message('ERROR: Database operation failed: ' . $e->getMessage());
                    }
                } else {
                    log_message('ERROR: Database connection variable ($conn) does not exist');

                    // Check what variables are defined
                    $defined_vars = array_keys(get_defined_vars());
                    log_message('Defined variables: ' . implode(', ', $defined_vars));
                }
            }
        } catch (Exception $e) {
            log_message('CRITICAL ERROR including db_conn.php: ' . $e->getMessage());
        }
    } else {
        throw new Exception('Could not find db_conn.php');
    }

    // Check if Stripe class exists
    if (class_exists('\Stripe\Stripe')) {
        log_message('Stripe class exists');

        // Get Stripe version
        if (defined('\Stripe\Stripe::VERSION')) {
            log_message('Stripe library version: ' . \Stripe\Stripe::VERSION);
        } else {
            log_message('Unable to determine Stripe library version');
        }

        // Try to set API key
        try {
            \Stripe\Stripe::setApiKey('sk_test_51OpK5NKnyip3i5w1YlQZg2XjdXWjIQ0UdTIXZHtkY1MoipKjhvMPurDOpSnf2v6fODGX1P5G9LWUSdafTr0rWsXy00U04R8JIH');
            log_message('Successfully set Stripe API key');

            // Try to make a simple API call
            try {
                $balance = \Stripe\Balance::retrieve();
                log_message('Successfully made API call to retrieve balance');
            } catch (Exception $e) {
                log_message('ERROR: Failed to make API call: ' . $e->getMessage());
            }

            // Check for Webhook class
            if (class_exists('\Stripe\Webhook')) {
                log_message('Stripe Webhook class exists');

                // Try to construct a test event
                try {
                    $payload = json_encode([
                        'id' => 'evt_test',
                        'type' => 'checkout.session.completed',
                        'data' => [
                            'object' => [
                                'id' => 'cs_test',
                                'client_reference_id' => 'TEST_STUDENT_ID',
                                'amount_total' => 10000,
                                'payment_intent' => 'pi_test'
                            ]
                        ]
                    ]);

                    log_message('Test payload created: ' . $payload);

                    // Try to parse the payload without signature verification
                    $data = json_decode($payload);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        log_message('Successfully parsed JSON payload');

                        // Check if we can access the required properties
                        if (isset($data->type)) {
                            log_message('Event type: ' . $data->type);

                            if (isset($data->data->object->client_reference_id)) {
                                log_message('client_reference_id: ' . $data->data->object->client_reference_id);
                            } else {
                                log_message('ERROR: client_reference_id not found in payload');
                            }

                            if (isset($data->data->object->amount_total)) {
                                log_message('amount_total: ' . $data->data->object->amount_total);
                            } else {
                                log_message('ERROR: amount_total not found in payload');
                            }

                            if (isset($data->data->object->payment_intent)) {
                                log_message('payment_intent: ' . $data->data->object->payment_intent);
                            } else {
                                log_message('ERROR: payment_intent not found in payload');
                            }
                        } else {
                            log_message('ERROR: Event type not found in payload');
                        }
                    } else {
                        log_message('ERROR: Failed to parse JSON payload: ' . json_last_error_msg());
                    }
                } catch (Exception $e) {
                    log_message('ERROR: Failed to construct test event: ' . $e->getMessage());
                }
            } else {
                log_message('ERROR: Stripe Webhook class does not exist');
            }
        } catch (Exception $e) {
            log_message('ERROR: Failed to set Stripe API key: ' . $e->getMessage());
        }
    } else {
        log_message('ERROR: Stripe class does not exist');

        // Check if any Stripe-related classes are available
        $stripe_classes = [
            '\Stripe\Account',
            '\Stripe\Customer',
            '\Stripe\Charge',
            '\Stripe\PaymentIntent',
            '\Stripe\Checkout\Session'
        ];

        foreach ($stripe_classes as $class) {
            if (class_exists($class)) {
                log_message('Class exists: ' . $class);
            } else {
                log_message('Class does not exist: ' . $class);
            }
        }
    }

    // Log request information
    log_message('Request Method: ' . $_SERVER['REQUEST_METHOD']);

    // Log headers
    $headers = getallheaders();
    $headers_str = json_encode($headers, JSON_PRETTY_PRINT);
    log_message('Headers: ' . $headers_str);

    // Get and log payload
    $payload = @file_get_contents('php://input');
    log_message('Payload: ' . $payload);

    // Check for Stripe signature
    $sig_header = isset($_SERVER['HTTP_STRIPE_SIGNATURE']) ? $_SERVER['HTTP_STRIPE_SIGNATURE'] : '';
    log_message('Stripe Signature: ' . $sig_header);

    // Try to construct event (but don't validate signature for debugging)
    if (!empty($payload)) {
        try {
            $data = json_decode($payload);
            if (json_last_error() === JSON_ERROR_NONE) {
                log_message('Successfully parsed JSON payload');

                // Log event type if available
                if (isset($data->type)) {
                    log_message('Event type: ' . $data->type);
                } else {
                    log_message('No event type in payload');
                }
            } else {
                log_message('ERROR: Failed to parse JSON payload: ' . json_last_error_msg());
            }
        } catch (Exception $e) {
            log_message('ERROR: Exception while processing payload: ' . $e->getMessage());
        }
    } else {
        log_message('Empty payload received');
    }

} catch (Exception $e) {
    log_message('CRITICAL ERROR: ' . $e->getMessage());
}

// End logging
log_message('Debug webhook handler completed');

// Return a 200 response to acknowledge receipt
http_response_code(200);
echo json_encode(['status' => 'success', 'message' => 'Debug webhook received and logged']);
?>
