<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
// Check if user is admin or cashier
if (!isset($_SESSION['role']) || ($_SESSION['role'] !== 'admin' && $_SESSION['role'] !== 'cashier')) {
    header("Location: ../login.php");
    exit();
}

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Function to check if a file exists and is readable
function check_file($file_path) {
    if (file_exists($file_path)) {
        if (is_readable($file_path)) {
            return ['status' => 'success', 'message' => 'File exists and is readable'];
        } else {
            return ['status' => 'warning', 'message' => 'File exists but is not readable'];
        }
    } else {
        return ['status' => 'error', 'message' => 'File does not exist'];
    }
}

// Function to check if a class exists
function check_class($class_name) {
    if (class_exists($class_name)) {
        return ['status' => 'success', 'message' => 'Class exists'];
    } else {
        return ['status' => 'error', 'message' => 'Class does not exist'];
    }
}

// Function to check if a function exists
function check_function($function_name) {
    if (function_exists($function_name)) {
        return ['status' => 'success', 'message' => 'Function exists'];
    } else {
        return ['status' => 'error', 'message' => 'Function does not exist'];
    }
}

// Function to check if an extension is loaded
function check_extension($extension_name) {
    if (extension_loaded($extension_name)) {
        return ['status' => 'success', 'message' => 'Extension is loaded'];
    } else {
        return ['status' => 'error', 'message' => 'Extension is not loaded'];
    }
}

// Initialize results array
$results = [];

// Check PHP version
$php_version = phpversion();
$results['php_version'] = [
    'name' => 'PHP Version',
    'value' => $php_version,
    'status' => version_compare($php_version, '7.0.0', '>=') ? 'success' : 'error',
    'message' => version_compare($php_version, '7.0.0', '>=') ? 'Compatible' : 'Stripe requires PHP 7.0.0 or higher'
];

// Check required extensions
$required_extensions = ['curl', 'json', 'mbstring'];
foreach ($required_extensions as $ext) {
    $check = check_extension($ext);
    $results['ext_' . $ext] = [
        'name' => 'Extension: ' . $ext,
        'value' => $check['status'] === 'success' ? 'Loaded' : 'Not loaded',
        'status' => $check['status'],
        'message' => $check['message']
    ];
}

// Check for vendor/autoload.php
$autoload_path = __DIR__ . '/vendor/autoload.php';
$check = check_file($autoload_path);
$results['autoload'] = [
    'name' => 'Composer Autoload',
    'value' => $autoload_path,
    'status' => $check['status'],
    'message' => $check['message']
];

// Try to include vendor/autoload.php
$stripe_loaded = false;
if ($check['status'] === 'success') {
    try {
        require $autoload_path;
        $results['autoload_include'] = [
            'name' => 'Include Autoload',
            'value' => 'Success',
            'status' => 'success',
            'message' => 'Successfully included autoload.php'
        ];
        $stripe_loaded = true;
    } catch (Exception $e) {
        $results['autoload_include'] = [
            'name' => 'Include Autoload',
            'value' => 'Failed',
            'status' => 'error',
            'message' => 'Error: ' . $e->getMessage()
        ];
    }
}

// Check for Stripe class
if ($stripe_loaded) {
    $check = check_class('\Stripe\Stripe');
    $results['stripe_class'] = [
        'name' => 'Stripe Class',
        'value' => '\Stripe\Stripe',
        'status' => $check['status'],
        'message' => $check['message']
    ];
    
    // Try to set API key
    if ($check['status'] === 'success') {
        try {
            \Stripe\Stripe::setApiKey('sk_test_51OpK5NKnyip3i5w1YlQZg2XjdXWjIQ0UdTIXZHtkY1MoipKjhvMPurDOpSnf2v6fODGX1P5G9LWUSdafTr0rWsXy00U04R8JIH');
            $results['stripe_api_key'] = [
                'name' => 'Set Stripe API Key',
                'value' => 'Success',
                'status' => 'success',
                'message' => 'Successfully set API key'
            ];
            
            // Try to make a simple API call
            try {
                $balance = \Stripe\Balance::retrieve();
                $results['stripe_api_call'] = [
                    'name' => 'Stripe API Call',
                    'value' => 'Success',
                    'status' => 'success',
                    'message' => 'Successfully made API call to retrieve balance'
                ];
            } catch (Exception $e) {
                $results['stripe_api_call'] = [
                    'name' => 'Stripe API Call',
                    'value' => 'Failed',
                    'status' => 'error',
                    'message' => 'Error: ' . $e->getMessage()
                ];
            }
        } catch (Exception $e) {
            $results['stripe_api_key'] = [
                'name' => 'Set Stripe API Key',
                'value' => 'Failed',
                'status' => 'error',
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }
}

// Check for database connection
$db_conn_path = __DIR__ . '/../db_conn.php';
$check = check_file($db_conn_path);
$results['db_conn'] = [
    'name' => 'Database Connection File',
    'value' => $db_conn_path,
    'status' => $check['status'],
    'message' => $check['message']
];

// Try to include database connection
if ($check['status'] === 'success') {
    try {
        require $db_conn_path;
        $results['db_conn_include'] = [
            'name' => 'Include Database Connection',
            'value' => 'Success',
            'status' => 'success',
            'message' => 'Successfully included db_conn.php'
        ];
        
        // Check if $conn variable exists
        if (isset($conn)) {
            $results['db_conn_var'] = [
                'name' => 'Database Connection Variable',
                'value' => '$conn',
                'status' => 'success',
                'message' => 'Database connection variable exists'
            ];
            
            // Test database connection
            if ($conn->ping()) {
                $results['db_conn_ping'] = [
                    'name' => 'Database Connection Test',
                    'value' => 'Success',
                    'status' => 'success',
                    'message' => 'Database connection is working'
                ];
            } else {
                $results['db_conn_ping'] = [
                    'name' => 'Database Connection Test',
                    'value' => 'Failed',
                    'status' => 'error',
                    'message' => 'Database connection failed: ' . $conn->error
                ];
            }
        } else {
            $results['db_conn_var'] = [
                'name' => 'Database Connection Variable',
                'value' => '$conn',
                'status' => 'error',
                'message' => 'Database connection variable does not exist'
            ];
        }
    } catch (Exception $e) {
        $results['db_conn_include'] = [
            'name' => 'Include Database Connection',
            'value' => 'Failed',
            'status' => 'error',
            'message' => 'Error: ' . $e->getMessage()
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stripe Configuration Check</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .check-item {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #ddd;
        }
        .check-item.success {
            background-color: #f0fff0;
            border-left-color: #4CAF50;
        }
        .check-item.warning {
            background-color: #fffbf0;
            border-left-color: #FFC107;
        }
        .check-item.error {
            background-color: #fff0f0;
            border-left-color: #F44336;
        }
        .check-item h3 {
            margin-top: 0;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        .check-item h3 i {
            margin-right: 10px;
        }
        .check-item .value {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
            display: inline-block;
        }
        .check-item .message {
            margin-top: 5px;
            font-size: 0.9rem;
        }
        .success-icon {
            color: #4CAF50;
        }
        .warning-icon {
            color: #FFC107;
        }
        .error-icon {
            color: #F44336;
        }
        .back-link {
            display: block;
            margin-top: 20px;
            text-align: center;
            color: #666;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-cogs"></i> Stripe Configuration Check</h1>
        
        <?php foreach ($results as $result): ?>
            <div class="check-item <?php echo $result['status']; ?>">
                <h3>
                    <?php if ($result['status'] === 'success'): ?>
                        <i class="fas fa-check-circle success-icon"></i>
                    <?php elseif ($result['status'] === 'warning'): ?>
                        <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <?php else: ?>
                        <i class="fas fa-times-circle error-icon"></i>
                    <?php endif; ?>
                    <?php echo $result['name']; ?>
                </h3>
                <div class="value"><?php echo $result['value']; ?></div>
                <div class="message"><?php echo $result['message']; ?></div>
            </div>
        <?php endforeach; ?>
        
        <a href="../cashier_dashboard.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</body>
</html>
