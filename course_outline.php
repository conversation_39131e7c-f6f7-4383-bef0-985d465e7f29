<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once 'db_conn.php';
require_once 'config.php';
require 'PHPMailer-6.9.2/src/PHPMailer.php';
require 'PHPMailer-6.9.2/src/SMTP.php';
require 'PHPMailer-6.9.2/src/Exception.php';
require 'fpdf186/fpdf.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Check if user is logged in and has registrar role
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'registrar') {
    http_response_code(403);
    echo "Unauthorized access";
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !validate_csrf_token($_POST['csrf_token'])) {
        http_response_code(403);
        $_SESSION['email_error'] = "Invalid request: security token missing or invalid";
        exit();
    }

    // Get student ID and email from POST data - sanitize inputs
    $studentId = isset($_POST['studentId']) ? sanitize_input($_POST['studentId']) : '';
    $studentEmail = isset($_POST['email']) ? filter_var($_POST['email'], FILTER_SANITIZE_EMAIL) : '';
    $selectedCourses = isset($_POST['courses']) ? array_map('intval', $_POST['courses']) : [];

    if (empty($studentId) || empty($studentEmail) || empty($selectedCourses)) {
        $_SESSION['email_error'] = "Missing required information";
        exit();
    }

    // Fetch student information
    $student_sql = "SELECT * FROM students WHERE student_id = ?";
    $student_stmt = $conn->prepare($student_sql);
    $student_stmt->bind_param("s", $studentId);
    $student_stmt->execute();
    $student_result = $student_stmt->get_result();

    if ($student_result->num_rows === 0) {
        $_SESSION['email_error'] = "Student not found";
        exit();
    }

    $student = $student_result->fetch_assoc();

    // Fetch courses for this student
    $courses_sql = "SELECT c.*
                  FROM courses c
                  WHERE c.program = ?
                  AND c.technology = ?
                  AND c.year_level = ?
                  ORDER BY c.course_id";
    $courses_stmt = $conn->prepare($courses_sql);
    $courses_stmt->bind_param("sss", $student['program'], $student['tech'], $student['year_level']);
    $courses_stmt->execute();
    $courses_result = $courses_stmt->get_result();

    // Store courses for later use
    $available_courses = [];
    while ($course = $courses_result->fetch_assoc()) {
        $available_courses[] = $course;
    }

    if (count($available_courses) === 0) {
        $_SESSION['email_error'] = "No courses found for this student's program, technology, and year level";
    }

    // Create PDF
    $pdf = new FPDF();
    $pdf->AddPage();
    $pdf->SetFont('Arial', 'B', 16);
    $pdf->Cell(0, 10, 'DON BOSCO TECHNOLOGICAL INSTITUTE', 0, 1, 'C');
    $pdf->SetFont('Arial', 'B', 14);
    $pdf->Cell(0, 10, 'Course Registration Details', 0, 1, 'C');
    $pdf->Ln(5);

    // Add student information
    $pdf->SetFont('Arial', 'B', 12);
    $pdf->Cell(0, 10, 'Student Information', 0, 1, 'L');
    $pdf->SetFont('Arial', '', 11);
    $pdf->Cell(40, 7, 'Student ID:', 0, 0, 'L');
    $pdf->Cell(0, 7, $studentId, 0, 1, 'L');
    $pdf->Cell(40, 7, 'Name:', 0, 0, 'L');
    $pdf->Cell(0, 7, $student['first_name'] . ' ' . $student['last_name'], 0, 1, 'L');
    $pdf->Cell(40, 7, 'Program:', 0, 0, 'L');
    $pdf->Cell(0, 7, $student['program'], 0, 1, 'L');
    $pdf->Cell(40, 7, 'Technology:', 0, 0, 'L');
    $pdf->Cell(0, 7, $student['tech'], 0, 1, 'L');
    $pdf->Cell(40, 7, 'Year Level:', 0, 0, 'L');
    $pdf->Cell(0, 7, $student['year_level'], 0, 1, 'L');
    $pdf->Ln(5);

    // Add course information
    $pdf->SetFont('Arial', 'B', 12);
    $pdf->Cell(0, 10, 'Registered Courses', 0, 1, 'L');

    // Create table header
    $pdf->SetFont('Arial', 'B', 10);
    $pdf->Cell(30, 7, 'Course ID', 1, 0, 'C');
    $pdf->Cell(80, 7, 'Course Name', 1, 0, 'C');
    $pdf->Cell(40, 7, 'Semester', 1, 0, 'C');
    $pdf->Cell(40, 7, 'Year Level', 1, 1, 'C');

    // Add course rows
    $pdf->SetFont('Arial', '', 10);
    foreach ($selectedCourses as $courseId) {
        $course_sql = "SELECT * FROM courses WHERE id = ?";
        $course_stmt = $conn->prepare($course_sql);
        $course_stmt->bind_param("i", $courseId);
        $course_stmt->execute();
        $course_result = $course_stmt->get_result();
        $course = $course_result->fetch_assoc();

        if ($course) {
            $pdf->Cell(30, 7, $course['course_id'], 1, 0, 'L');
            $pdf->Cell(80, 7, $course['course_name'], 1, 0, 'L');
            $pdf->Cell(40, 7, $course['semester'], 1, 0, 'L');
            $pdf->Cell(40, 7, $course['year_level'], 1, 1, 'L');
        }
    }

    // Add footer
    $pdf->Ln(10);
    $pdf->SetFont('Arial', 'I', 10);
    $pdf->Cell(0, 7, 'This document serves as proof of course registration.', 0, 1, 'L');
    $pdf->Cell(0, 7, 'Date Generated: ' . date('d/m/Y h:i A'), 0, 1, 'L');

    // Generate a secure random filename for the PDF
    $pdf_file = TEMP_DIR . generate_secure_filename('pdf');
    $pdf->Output('F', $pdf_file);

    // Send email with PDF attachment using configuration from config.php
    $mail = new PHPMailer();
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = SMTP_AUTH;
        $mail->Username = REGISTRAR_EMAIL;
        $mail->Password = REGISTRAR_PASSWORD;
        $mail->SMTPSecure = SMTP_SECURE;
        $mail->Port = SMTP_PORT;

        // Recipients
        $mail->setFrom(REGISTRAR_EMAIL, 'DBTI ONLINE REGISTRATION');
        $mail->addAddress($studentEmail, $student['first_name'] . ' ' . $student['last_name']);

        // Attach PDF
        $mail->addAttachment($pdf_file, 'Course_Registration.pdf');

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Course Registration - ' . $student['first_name'] . ' ' . $student['last_name'];
        $mail->Body = 'Dear ' . $student['first_name'] . ' ' . $student['last_name'] . ',<br><br>' .
                     'Thank you for your registration. Please find attached your course registration details.<br><br>' .
                     'Regards,<br>Registrar, DON BOSCO TECHNOLOGICAL INSTITUTE';

        $mail->send();
        echo 'Course registration sent successfully to ' . htmlspecialchars($studentEmail);
    } catch (Exception $e) {
        echo 'Message could not be sent. Mailer Error: ' . $mail->ErrorInfo;
    }

    // Clean up PDF file
    if (file_exists($pdf_file)) {
        unlink($pdf_file);
    }
}

// Get student ID and email from URL parameters
$studentId = isset($_GET['studentId']) ? $_GET['studentId'] : '';
$studentEmail = isset($_GET['email']) ? $_GET['email'] : '';

// Verify we have required parameters
if (empty($studentId) || empty($studentEmail)) {
    $_SESSION['email_error'] = "Missing required parameters (student ID or email)";
} else {
    // Fetch student information
    $student_sql = "SELECT * FROM students WHERE student_id = ?";
    $student_stmt = $conn->prepare($student_sql);
    $student_stmt->bind_param("s", $studentId);
    $student_stmt->execute();
    $student_result = $student_stmt->get_result();

    if ($student_result->num_rows === 0) {
        $_SESSION['email_error'] = "Student not found with ID: $studentId";
    } else {
        $student = $student_result->fetch_assoc();

        // Fetch courses for this student
        $courses_sql = "SELECT c.*
                      FROM courses c
                      WHERE c.program = ?
                      AND c.technology = ?
                      AND c.year_level = ?
                      ORDER BY c.course_id";
        $courses_stmt = $conn->prepare($courses_sql);
        $courses_stmt->bind_param("sss", $student['program'], $student['tech'], $student['year_level']);
        $courses_stmt->execute();
        $courses_result = $courses_stmt->get_result();

        // Store courses for later use
        $available_courses = [];
        while ($course = $courses_result->fetch_assoc()) {
            $available_courses[] = $course;
        }

        if (count($available_courses) === 0) {
            $_SESSION['email_error'] = "No courses found for this student's program, technology, and year level";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Course Registration Email Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .email-form {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #ffbf00;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="email"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .checkbox-group {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
        }
        .course-item {
            padding: 8px;
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .course-item:last-child {
            border-bottom: none;
        }
        .submit-btn {
            background-color: #ffbf00;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        .submit-btn:hover {
            background-color: #e0a800;
        }
    </style>
</head>
<body>
    <div class="email-form">
        <h2>Send Course Registration Details</h2>

        <?php if (isset($_SESSION['email_error'])): ?>
            <div style="color: #dc3545; padding: 10px; margin-bottom: 15px; border: 1px solid #dc3545; border-radius: 4px;">
                <?php echo $_SESSION['email_error']; unset($_SESSION['email_error']); ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <!-- CSRF Protection -->
            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($studentEmail); ?>" readonly>
            </div>

            <div class="form-group">
                <label for="fullName">Full Name:</label>
                <input type="text" id="fullName" name="fullName" value="<?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>" readonly>
            </div>

            <div class="form-group">
                <label for="studentId">Student ID:</label>
                <input type="text" id="studentId" name="studentId" value="<?php echo htmlspecialchars($studentId); ?>" readonly>
            </div>

            <div class="form-group">
                <h3>Select Courses to Include</h3>
                <div class="checkbox-group">
                    <?php foreach ($available_courses as $course): ?>
                        <div class="course-item">
                            <label>
                                <input type="checkbox" name="courses[]" value="<?php echo $course['id']; ?>" checked>
                                <strong><?php echo htmlspecialchars($course['course_id']); ?></strong> -
                                <?php echo htmlspecialchars($course['course_name']); ?>
                                (<?php echo htmlspecialchars($course['semester']); ?>)
                            </label>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <button type="submit" class="submit-btn">Send Course Registration</button>
        </form>
    </div>
</body>
</html>
