<?php
session_start();
require_once 'db_conn.php';

// Verify user is logged in and has registrar role
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'registrar') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check if student_id is provided
if (!isset($_GET['student_id']) || empty($_GET['student_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Student ID is required']);
    exit();
}

$student_id = $_GET['student_id'];

// Get registration history for the student
$sql = "SELECT r.registration_id, r.registration_date, r.semester, r.academic_year, 
        GROUP_CONCAT(c.course_name SEPARATOR ', ') as courses
        FROM registrations r
        LEFT JOIN courses c ON r.course_id = c.id
        WHERE r.student_id = ?
        GROUP BY r.registration_date, r.semester, r.academic_year
        ORDER BY r.registration_date DESC";

$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $student_id);
$stmt->execute();
$result = $stmt->get_result();

$history = [];
while ($row = $result->fetch_assoc()) {
    $history[] = $row;
}

header('Content-Type: application/json');
echo json_encode(['success' => true, 'history' => $history]);
?>
