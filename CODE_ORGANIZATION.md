# Code Organization Guide

This document outlines the new code organization structure for the DBTI Online Registration System. The goal of this reorganization is to reduce code duplication, improve maintainability, and enhance the overall structure of the codebase.

## Directory Structure

```
dbtionline/
├── assets/
│   ├── css/
│   │   ├── styles.css (Main CSS file)
│   │   └── ... (Other CSS files)
│   ├── js/
│   │   ├── main.js (Main JavaScript file)
│   │   └── ... (Other JS files)
│   └── img/ (Images)
├── includes/
│   ├── classes/
│   │   ├── Database.php (Database operations)
│   │   ├── Authentication.php (Authentication operations)
│   │   └── ... (Other classes)
│   ├── functions/
│   │   ├── utilities.php (Utility functions)
│   │   └── ... (Other function files)
│   ├── templates/
│   │   ├── header.php (Header template)
│   │   ├── footer.php (Footer template)
│   │   ├── home.php (Home page content)
│   │   ├── student_dashboard.php (Student dashboard content)
│   │   └── ... (Other templates)
│   └── init.php (Initialization file)
├── temp/ (Temporary files)
├── uploads/ (Uploaded files)
├── logs/ (Log files)
├── cache/ (Cache files)
├── config.php (Configuration file)
├── index.php (Home page)
├── login.php (Login page)
├── student_dashboard.php (Student dashboard)
└── ... (Other pages)
```

## Key Components

### 1. Database Class

The `Database` class (`includes/classes/Database.php`) centralizes all database operations:

- Singleton pattern for database connection
- Prepared statements for all queries
- Methods for common operations (fetch, insert, update, delete)
- Error handling and logging

### 2. Authentication Class

The `Authentication` class (`includes/classes/Authentication.php`) handles user authentication:

- Login and logout functionality
- Role-based access control
- Session management
- Password hashing and verification

### 3. Template System

The template system separates HTML from PHP logic:

- `header.php` and `footer.php` for common page elements
- Content templates for specific pages
- `load_template()` and `load_page()` functions for loading templates

### 4. Utility Functions

Utility functions (`includes/functions/utilities.php`) provide common functionality:

- Flash messages
- Date and currency formatting
- URL redirection
- Form validation
- File handling

### 5. Initialization File

The `init.php` file loads all required files and initializes the application:

- Loads configuration
- Starts session
- Includes classes and functions
- Initializes database connection
- Defines helper functions

## Usage Examples

### Loading a Page

```php
// Load initialization file
require_once 'includes/init.php';

// Set page options
$options = [
    'title' => 'Page Title - ' . APP_NAME,
    'description' => 'Page description',
    'additional_css' => ['/assets/css/custom.css'],
    'additional_js' => ['/assets/js/custom.js']
];

// Load the page with a template
load_page('template_name', $data, $options);
```

### Database Operations

```php
// Get database instance
$db = Database::getInstance();

// Fetch a single row
$user = $db->fetchRow("SELECT * FROM users WHERE id = ?", [$user_id]);

// Fetch multiple rows
$courses = $db->fetchAll("SELECT * FROM courses WHERE program = ?", [$program]);

// Insert data
$user_id = $db->insert('users', [
    'username' => $username,
    'password' => $password_hash,
    'email' => $email
]);

// Update data
$db->update('users', 
    ['email' => $new_email], 
    'id = ?', 
    [$user_id]
);

// Delete data
$db->delete('users', 'id = ?', [$user_id]);
```

### Authentication

```php
// Get authentication instance
$auth = Authentication::getInstance();

// Authenticate user
if ($auth->authenticate($username, $password)) {
    redirect('dashboard.php');
} else {
    set_flash_message('Invalid username or password', 'danger');
    redirect('login.php');
}

// Require specific role
$auth->requireRole('admin', 'login.php');

// Check if user has role
if ($auth->hasRole(['admin', 'registrar'])) {
    // Show admin content
}
```

### Flash Messages

```php
// Set a flash message
set_flash_message('Operation completed successfully', 'success');

// Redirect to another page
redirect('dashboard.php');
```

## Benefits of the New Structure

1. **Reduced Code Duplication**: Common functionality is centralized in classes and functions.
2. **Improved Maintainability**: Changes to common functionality only need to be made in one place.
3. **Enhanced Security**: Centralized security checks and database operations.
4. **Better Organization**: Clear separation of concerns between different parts of the application.
5. **Easier Debugging**: Standardized error handling and logging.
6. **Simplified Development**: Reusable components make adding new features easier.

## Migration Guide

To migrate existing pages to the new structure:

1. Replace direct database queries with Database class methods
2. Replace authentication code with Authentication class methods
3. Move HTML to template files
4. Update page files to use the template system
5. Replace utility code with utility functions

## Conclusion

This new code organization structure provides a solid foundation for the DBTI Online Registration System. By centralizing common functionality and separating concerns, the codebase becomes more maintainable, secure, and easier to extend.
