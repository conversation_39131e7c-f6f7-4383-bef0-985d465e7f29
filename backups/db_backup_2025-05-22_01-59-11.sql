-- Database Backup
-- Generated: 2025-05-22 01:59:11
-- SQLite Database File: /home/<USER>/Documents/git/dbtionline/database/dbtionline.sqlite

-- SQLite version: 3.40.1

-- Note: This is a SQLite database backup
-- To restore, execute these SQL statements in sequence

PRAGMA foreign_keys = OFF;
BEGIN TRANSACTION;

-- Table structure for table `students`
DROP TABLE IF EXISTS `students`;
CREATE TABLE `students` (
  `student_id` TEXT NOT NULL PRIMARY KEY,
  `first_name` TEXT NOT NULL,
  `last_name` TEXT NOT NULL,
  `gender` TEXT NOT NULL,
  `dob` DATE NOT NULL,
  `phone_number` TEXT NOT NULL,
  `student_email` TEXT NOT NULL,
  `residential_address` TEXT NOT NULL,
  `home_province` TEXT NOT NULL,
  `guardian_name` TEXT NOT NULL,
  `guardian_occupation` TEXT NOT NULL,
  `guardian_phone_number` TEXT NOT NULL,
  `guardian_email` TEXT NOT NULL,
  `program` TEXT DEFAULT NULL,
  `tech` TEXT DEFAULT NULL,
  `year_level` TEXT DEFAULT 'Temporary',
  `payment_status` TEXT DEFAULT 'not_paid',
  `registration_status` TEXT DEFAULT 'unregistered',
  `registration_timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX `idx_student_email` ON `students` (`student_email`);

-- Dumping data for table `students`
INSERT INTO `students` VALUES ('ADMIN','System','Administrator','Other','2000-01-01','000-0000','<EMAIL>','System','System','System','System','000-0000','<EMAIL>',NULL,NULL,'Temporary','not_paid','unregistered','2025-05-22 01:55:47');

-- Table structure for table `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `username` TEXT NOT NULL,
  `student_id` TEXT DEFAULT NULL,
  `password` TEXT NOT NULL,
  `role` TEXT NOT NULL,
  `last_login` DATETIME DEFAULT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`) ON DELETE SET NULL ON UPDATE CASCADE
);
CREATE UNIQUE INDEX `username` ON `users` (`username`);
CREATE INDEX `fk_user_student` ON `users` (`student_id`);
CREATE UNIQUE INDEX `idx_username` ON `users` (`username`);

-- Dumping data for table `users`
INSERT INTO `users` VALUES ('1','admin','ADMIN','$2y$10$Hjus6CIzxBYQnrEOiPvOEOJfPoZ7LFpIB40SKbTEPG6prabbsBpgS','admin',NULL,'2025-05-22 01:55:48','2025-05-22 01:55:48');

-- Table structure for table `courses`
DROP TABLE IF EXISTS `courses`;
CREATE TABLE `courses` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `course_id` TEXT NOT NULL,
  `course_name` TEXT NOT NULL,
  `program` TEXT NOT NULL,
  `technology` TEXT NOT NULL,
  `year_level` TEXT NOT NULL,
  `semester` TEXT NOT NULL DEFAULT 'Semester 1',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX `idx_course_id` ON `courses` (`course_id`);

-- No data for table `courses`

-- Table structure for table `registrations`
DROP TABLE IF EXISTS `registrations`;
CREATE TABLE `registrations` (
  `registration_id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `student_id` TEXT NOT NULL,
  `registration_date` DATETIME NOT NULL,
  `semester` TEXT NOT NULL,
  `academic_year` TEXT NOT NULL,
  `status` TEXT NOT NULL DEFAULT 'current',
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX `student_id` ON `registrations` (`student_id`);
CREATE INDEX `idx_registration_date` ON `registrations` (`registration_date`);
CREATE INDEX `idx_student_id` ON `registrations` (`student_id`);

-- No data for table `registrations`

-- Table structure for table `course_registrations`
DROP TABLE IF EXISTS `course_registrations`;
CREATE TABLE `course_registrations` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `registration_id` INTEGER NOT NULL,
  `course_id` TEXT NOT NULL,
  FOREIGN KEY (`registration_id`) REFERENCES `registrations`(`registration_id`) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX `registration_id` ON `course_registrations` (`registration_id`);
CREATE INDEX `idx_registration_id` ON `course_registrations` (`registration_id`);

-- No data for table `course_registrations`

-- Table structure for table `payments`
DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments` (
  `payment_id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `student_id` TEXT NOT NULL,
  `amount` REAL NOT NULL,
  `payment_date` DATETIME NOT NULL,
  `payment_method` TEXT NOT NULL,
  `transaction_id` TEXT DEFAULT NULL,
  `payment_status` TEXT DEFAULT 'completed',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`) ON DELETE CASCADE ON UPDATE CASCADE
);
CREATE INDEX `idx_payment_date` ON `payments` (`payment_date`);

-- No data for table `payments`

-- Table structure for table `users_temp`
DROP TABLE IF EXISTS `users_temp`;
CREATE TABLE `users_temp` (
  `user_id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `username` TEXT NOT NULL,
  `student_id` TEXT DEFAULT 'NULL',
  `password` TEXT NOT NULL,
  `role` TEXT NOT NULL,
  `last_login` DATETIME DEFAULT 'NULL',
  `created_at` TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP',
  `updated_at` TIMESTAMP DEFAULT 'CURRENT_TIMESTAMP',
  FOREIGN KEY (`student_id`) REFERENCES `students`(`student_id`) ON DELETE SET NULL ON UPDATE CASCADE
);

-- No data for table `users_temp`

-- Table structure for table `course_registrations_temp`
DROP TABLE IF EXISTS `course_registrations_temp`;
CREATE TABLE `course_registrations_temp` (
  `id` INTEGER PRIMARY KEY AUTOINCREMENT,
  `registration_id` INTEGER NOT NULL,
  `course_id` TEXT NOT NULL,
  FOREIGN KEY (`registration_id`) REFERENCES `registrations`(`registration_id`) ON DELETE CASCADE ON UPDATE CASCADE
);

-- No data for table `course_registrations_temp`

COMMIT;
PRAGMA foreign_keys = ON;
