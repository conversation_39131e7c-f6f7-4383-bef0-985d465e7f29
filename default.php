<?php
// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration
require_once 'config.php';

// Session timeout check using the function from security_helpers.php
if (session_expired()) {
    session_unset();
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Admin Dashboard - DBTI Online Registration">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>Admin Dashboard - DBTI Online Registration</title>
    <style>
        * {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}



        .adminheading {
            text-align: center;
        }
        /* Custom CSS to position the button at the top-right */
        .view-users-button {
            position: absolute;
            right: 20px;
            top: 20px;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 16px;
        }

        .view-users-button:hover {
            background-color: #0056b3;
        }

                /* Footer */
footer {
    background-color: #2d3748;
    color: #ecf0f1;
    padding: 40px 5%;
    text-align: center;
    position: fixed; /* Ensure footer is relative for fixed positioning */
    bottom: 0; /* Stick to bottom */
    width: 100%; /* Full width */
}

footer a {
    color: #e74c3c;
    text-decoration: none;
    font-weight: bold;
}

footer a:hover {
    text-decoration: underline;
}
    </style>
</head>
<body>
    <nav>
        <div id="yt_logo" class="col-md-3 col-sm-12">
            <a class="yt_logo" href="#" title="Don Bosco Technological Institute">
                <img src="img/logo.webp" alt="DBTI Logo">
            </a>
        </div>
        <div class="heading">DBTI Online Registration</div>
        <span class="sideMenuButton" onclick="openNavbar()">
            &#9776;
        </span>
        <div class="navbar">
            <ul>
                <li><a href="index.php">Home</a></li>
                <li><a href="#">About</a></li>
                <li><a href="admin_dashboard.php">Dashboard</a></li>
                <li><a href="logout.php">Logout </a> <i class="fa-solid fa-user"></i></li>
            </ul>
        </div>
    </nav>

    <!-- Side navigation bar for responsive website -->
    <div class="sideNavigationBar" id="sideNavigationBar">
        <a href="#" class="closeButton" onclick="closeNavbar()">
            &#x274C;
        </a>
        <a href="index.php">Home</a>
        <a href="#">About</a>
        <a href="admin_dashboard.php">Dashboard</a>
        <a href="logout.php">Logout</a>
    </div>

    <div class="container">
        <a href="users.php" class="view-users-button">View All Users</a> <!-- Positioned button -->

        <div class="main-content">
            <h2 class="adminheading">Admin Dashboard</h2><br>
            <h3>Create New User</h3><br>
            <form action="create_user.php" method="post">
                <label for="student_id">User:</label>
                <input type="text" id="student_id" name="student_id" required>

                <label for="first_name">First Name:</label>
                <input type="text" id="first_name" name="first_name" required>

                <label for="last_name">Last Name:</label>
                <input type="text" id="last_name" name="last_name" required>

                <label for="new_password">Password:</label>
                <input type="password" id="new_password" name="new_password" required>

                <label for="role">Role:</label>
                <select id="role" name="role" required>
                    <option value="student">Student</option>
                    <option value="cashier">Cashier</option>
                    <option value="registrar">Registrar</option>
                </select>

                <button type="submit">Create User</button>
            </form>
        </div>
    </div>
    <footer>
        <div class="footer">
            <span>
                Copyright &#169; 2024 Don Bosco Technological Institute. All Rights Reserved.
                <a href="https://www.dbti.ac.pg/" target="_blank">
                    DBTI Website
                </a>
            </span>
        </div>
    </footer>
    <script src="script.js"></script>
</body>
</html>



