<?php
session_start();
require_once 'db_conn.php';

// Check if user is logged in
if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'student') {
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Get parameters
$program = isset($_GET['program']) ? $_GET['program'] : '';
$tech = isset($_GET['tech']) ? $_GET['tech'] : '';
$year = isset($_GET['year']) ? $_GET['year'] : '';

// Validate parameters
if (empty($program) || empty($tech) || empty($year)) {
    echo json_encode(['error' => 'Missing parameters']);
    exit();
}

// Query to get courses based on program, tech, and year level
$sql = "SELECT course_id, course_code, course_name, credits 
        FROM courses 
        WHERE program = ? AND tech = ? AND year_level = ?
        ORDER BY course_code";

$stmt = $conn->prepare($sql);
$stmt->bind_param("sss", $program, $tech, $year);
$stmt->execute();
$result = $stmt->get_result();

$courses = [];
while ($row = $result->fetch_assoc()) {
    $courses[] = $row;
}

echo json_encode(['courses' => $courses]);
?>
