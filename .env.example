# Database Configuration
DB_HOST=localhost
DB_USER=database_username
DB_PASSWORD=database_password
DB_NAME=database_name

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=465
SMTP_SECURE=ssl
SMTP_AUTH=true
REGISTRAR_EMAIL=<EMAIL>
REGISTRAR_PASSWORD=email_password
CASHIER_EMAIL=<EMAIL>
CASHIER_PASSWORD=email_password

# Application Settings
APP_URL=https://example.com

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
