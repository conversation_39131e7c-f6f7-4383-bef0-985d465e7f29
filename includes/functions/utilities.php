<?php
/**
 * Utility Functions
 * 
 * This file contains utility functions used throughout the application
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS') && basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    header('HTTP/1.1 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Set a flash message to be displayed on the next page load
 * 
 * @param string $message The message to display
 * @param string $type The type of message (info, success, warning, danger)
 * @return void
 */
function set_flash_message($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

/**
 * Get the flash message if one exists
 * 
 * @return array|null The message and type, or null if no message exists
 */
function get_flash_message() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        
        // Clear the message
        unset($_SESSION['message']);
        unset($_SESSION['message_type']);
        
        return [
            'message' => $message,
            'type' => $type
        ];
    }
    
    return null;
}

/**
 * Redirect to a URL
 * 
 * @param string $url The URL to redirect to
 * @param int $status The HTTP status code
 * @return void
 */
function redirect($url, $status = 302) {
    header("Location: $url", true, $status);
    exit();
}

/**
 * Format a date
 * 
 * @param string $date The date to format
 * @param string $format The format to use
 * @return string The formatted date
 */
function format_date($date, $format = 'Y-m-d') {
    return date($format, strtotime($date));
}

/**
 * Format a currency amount
 * 
 * @param float $amount The amount to format
 * @param string $currency The currency symbol
 * @return string The formatted amount
 */
function format_currency($amount, $currency = 'K') {
    return $currency . number_format($amount, 2);
}

/**
 * Generate a random string
 * 
 * @param int $length The length of the string
 * @return string The random string
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[random_int(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * Get the current academic year
 * 
 * @return string The current academic year
 */
function get_current_academic_year() {
    $year = date('Y');
    $month = date('n');
    
    // If it's before August, use the previous year
    if ($month < 8) {
        $year--;
    }
    
    return $year . '-' . ($year + 1);
}

/**
 * Get the current semester
 * 
 * @return string The current semester (1 or 2)
 */
function get_current_semester() {
    $month = date('n');
    
    // First semester: August to January
    // Second semester: February to July
    return ($month >= 2 && $month <= 7) ? '2' : '1';
}

/**
 * Check if a student has paid the required fee
 * 
 * @param string $student_id The student ID
 * @param float $required_fee The required fee amount
 * @return bool True if the student has paid, false otherwise
 */
function has_paid_fee($student_id, $required_fee) {
    $db = Database::getInstance();
    
    $sql = "SELECT SUM(amount) as total_paid FROM payments WHERE student_id = ?";
    $result = $db->fetchRow($sql, [$student_id]);
    
    $total_paid = $result['total_paid'] ?? 0;
    
    return $total_paid >= $required_fee;
}

/**
 * Get a student's information
 * 
 * @param string $student_id The student ID
 * @return array|null The student information, or null if not found
 */
function get_student_info($student_id) {
    $db = Database::getInstance();
    
    $sql = "SELECT * FROM students WHERE student_id = ?";
    return $db->fetchRow($sql, [$student_id]);
}

/**
 * Get a list of courses for a student
 * 
 * @param string $student_id The student ID
 * @param string $academic_year The academic year
 * @param string $semester The semester
 * @return array The courses
 */
function get_student_courses($student_id, $academic_year = null, $semester = null) {
    $db = Database::getInstance();
    
    $params = [$student_id];
    $sql = "SELECT c.course_id, c.course_name, c.credits, r.semester, r.academic_year 
            FROM registrations r 
            JOIN courses c ON r.course_id = c.course_id 
            WHERE r.student_id = ?";
    
    if ($academic_year) {
        $sql .= " AND r.academic_year = ?";
        $params[] = $academic_year;
    }
    
    if ($semester) {
        $sql .= " AND r.semester = ?";
        $params[] = $semester;
    }
    
    $sql .= " ORDER BY c.course_id";
    
    return $db->fetchAll($sql, $params);
}

/**
 * Get a list of available programs
 * 
 * @return array The programs
 */
function get_programs() {
    $db = Database::getInstance();
    
    $sql = "SELECT DISTINCT program FROM courses ORDER BY program";
    $result = $db->fetchAll($sql);
    
    $programs = [];
    foreach ($result as $row) {
        $programs[] = $row['program'];
    }
    
    return $programs;
}

/**
 * Get a list of technologies for a program
 * 
 * @param string $program The program
 * @return array The technologies
 */
function get_technologies($program) {
    $db = Database::getInstance();
    
    $sql = "SELECT DISTINCT technology FROM courses WHERE program = ? ORDER BY technology";
    $result = $db->fetchAll($sql, [$program]);
    
    $technologies = [];
    foreach ($result as $row) {
        $technologies[] = $row['technology'];
    }
    
    return $technologies;
}

/**
 * Get a list of year levels for a program and technology
 * 
 * @param string $program The program
 * @param string $technology The technology
 * @return array The year levels
 */
function get_year_levels($program, $technology) {
    $db = Database::getInstance();
    
    $sql = "SELECT DISTINCT year_level FROM courses 
            WHERE program = ? AND technology = ? 
            ORDER BY year_level";
    $result = $db->fetchAll($sql, [$program, $technology]);
    
    $year_levels = [];
    foreach ($result as $row) {
        $year_levels[] = $row['year_level'];
    }
    
    return $year_levels;
}

/**
 * Get a list of courses for a program, technology, and year level
 * 
 * @param string $program The program
 * @param string $technology The technology
 * @param string $year_level The year level
 * @param string $semester The semester
 * @return array The courses
 */
function get_courses($program, $technology, $year_level, $semester = null) {
    $db = Database::getInstance();
    
    $params = [$program, $technology, $year_level];
    $sql = "SELECT course_id, course_name, credits, semester 
            FROM courses 
            WHERE program = ? AND technology = ? AND year_level = ?";
    
    if ($semester) {
        $sql .= " AND semester = ?";
        $params[] = $semester;
    }
    
    $sql .= " ORDER BY course_id";
    
    return $db->fetchAll($sql, $params);
}
