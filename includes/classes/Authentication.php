<?php
/**
 * Authentication Class
 * 
 * Handles user authentication and authorization
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS') && basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    header('HTTP/1.1 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

class Authentication {
    private $db;
    private static $instance = null;
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get singleton instance
     * 
     * @return Authentication The authentication instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Authenticate a user
     * 
     * @param string $username The username
     * @param string $password The password
     * @return bool True if authentication is successful, false otherwise
     */
    public function authenticate($username, $password) {
        // Sanitize username
        $username = sanitize_input($username);
        
        // Get user from database
        $sql = "SELECT * FROM users WHERE username = ?";
        $user = $this->db->fetchRow($sql, [$username]);
        
        if (!$user) {
            // User not found
            error_log("Failed login attempt - User not found: $username");
            return false;
        }
        
        // Verify password
        if (password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['last_activity'] = time();
            
            // Regenerate session ID to prevent session fixation
            session_regenerate_id(true);
            
            // Log successful login
            error_log("User {$user['username']} logged in successfully");
            
            return true;
        } else {
            // Invalid password
            error_log("Failed login attempt for user: $username - Invalid password");
            return false;
        }
    }
    
    /**
     * Check if user is logged in
     * 
     * @return bool True if user is logged in, false otherwise
     */
    public function isLoggedIn() {
        return isset($_SESSION['username']) && isset($_SESSION['role']);
    }
    
    /**
     * Check if user has a specific role
     * 
     * @param string|array $roles The role(s) to check
     * @return bool True if user has the role, false otherwise
     */
    public function hasRole($roles) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // Convert single role to array
        if (!is_array($roles)) {
            $roles = [$roles];
        }
        
        return in_array($_SESSION['role'], $roles);
    }
    
    /**
     * Require user to be logged in
     * 
     * @param string $redirectUrl The URL to redirect to if not logged in
     */
    public function requireLogin($redirectUrl = 'login.php') {
        if (!$this->isLoggedIn()) {
            header("Location: $redirectUrl");
            exit();
        }
        
        // Check if session has expired
        if (session_expired()) {
            $this->logout();
            header("Location: $redirectUrl");
            exit();
        }
    }
    
    /**
     * Require user to have a specific role
     * 
     * @param string|array $roles The role(s) to require
     * @param string $redirectUrl The URL to redirect to if not authorized
     */
    public function requireRole($roles, $redirectUrl = 'login.php') {
        $this->requireLogin($redirectUrl);
        
        if (!$this->hasRole($roles)) {
            header("Location: $redirectUrl");
            exit();
        }
    }
    
    /**
     * Get current user's username
     * 
     * @return string|null The username or null if not logged in
     */
    public function getUsername() {
        return $_SESSION['username'] ?? null;
    }
    
    /**
     * Get current user's role
     * 
     * @return string|null The role or null if not logged in
     */
    public function getRole() {
        return $_SESSION['role'] ?? null;
    }
    
    /**
     * Get current user's ID
     * 
     * @return int|null The user ID or null if not logged in
     */
    public function getUserId() {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Get current user's information
     * 
     * @return array|null The user information or null if not logged in
     */
    public function getUserInfo() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        $sql = "SELECT * FROM users WHERE username = ?";
        return $this->db->fetchRow($sql, [$_SESSION['username']]);
    }
    
    /**
     * Logout the current user
     */
    public function logout() {
        // Unset all session variables
        $_SESSION = [];
        
        // If it's desired to kill the session, also delete the session cookie.
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Finally, destroy the session.
        session_destroy();
    }
    
    /**
     * Register a new user
     * 
     * @param string $username The username
     * @param string $password The password
     * @param string $role The role
     * @param array $additionalData Additional data for the user
     * @return int|bool The user ID if successful, false otherwise
     */
    public function registerUser($username, $password, $role, $additionalData = []) {
        // Validate password
        $passwordValidation = validate_password($password);
        if (!$passwordValidation['status']) {
            return false;
        }
        
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Prepare data
        $userData = [
            'username' => $username,
            'password' => $hashedPassword,
            'role' => $role
        ];
        
        // Add additional data
        $userData = array_merge($userData, $additionalData);
        
        // Insert user
        try {
            return $this->db->insert('users', $userData);
        } catch (Exception $e) {
            error_log("Failed to register user: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update user password
     * 
     * @param string $username The username
     * @param string $newPassword The new password
     * @return bool True if successful, false otherwise
     */
    public function updatePassword($username, $newPassword) {
        // Validate password
        $passwordValidation = validate_password($newPassword);
        if (!$passwordValidation['status']) {
            return false;
        }
        
        // Hash password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // Update password
        try {
            $this->db->update('users', ['password' => $hashedPassword], 'username = ?', [$username]);
            return true;
        } catch (Exception $e) {
            error_log("Failed to update password: " . $e->getMessage());
            return false;
        }
    }
}
