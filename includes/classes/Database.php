<?php
/**
 * Database Class
 * 
 * Handles all database operations for the application
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS') && basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    header('HTTP/1.1 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

class Database {
    private $conn;
    private static $instance = null;
    
    /**
     * Constructor - establishes database connection
     */
    private function __construct() {
        $servername = DB_HOST;
        $username = DB_USER;
        $password = DB_PASSWORD;
        $dbname = DB_NAME;
        
        // Create connection
        $this->conn = new mysqli($servername, $username, $password, $dbname);
        
        // Check connection
        if ($this->conn->connect_error) {
            error_log("Database connection failed: " . $this->conn->connect_error);
            throw new Exception("Database connection failed. Please contact the administrator.");
        }
        
        // Set charset to ensure proper encoding
        $this->conn->set_charset("utf8mb4");
    }
    
    /**
     * Get singleton instance
     * 
     * @return Database The database instance
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Get the database connection
     * 
     * @return mysqli The database connection
     */
    public function getConnection() {
        return $this->conn;
    }
    
    /**
     * Execute a query with prepared statement
     * 
     * @param string $sql The SQL query
     * @param array $params The parameters for the query
     * @param string $types The types of the parameters (e.g., "ssi" for string, string, integer)
     * @return mysqli_result|bool The result of the query
     */
    public function query($sql, $params = [], $types = '') {
        $stmt = $this->conn->prepare($sql);
        
        if ($stmt === false) {
            error_log("Failed to prepare statement: " . $this->conn->error);
            throw new Exception("Database error. Please try again later.");
        }
        
        // If there are parameters, bind them
        if (!empty($params)) {
            // If types is not provided, generate it based on the parameters
            if (empty($types)) {
                $types = $this->generateTypes($params);
            }
            
            // Convert params to references for bind_param
            $bindParams = [];
            $bindParams[] = &$types;
            
            foreach ($params as &$param) {
                $bindParams[] = &$param;
            }
            
            call_user_func_array([$stmt, 'bind_param'], $bindParams);
        }
        
        // Execute the statement
        $stmt->execute();
        
        // Check for errors
        if ($stmt->errno) {
            error_log("Failed to execute statement: " . $stmt->error);
            throw new Exception("Database error. Please try again later.");
        }
        
        // Get the result
        $result = $stmt->get_result();
        
        // Close the statement
        $stmt->close();
        
        return $result;
    }
    
    /**
     * Fetch a single row from the database
     * 
     * @param string $sql The SQL query
     * @param array $params The parameters for the query
     * @param string $types The types of the parameters
     * @return array|null The fetched row or null if no row found
     */
    public function fetchRow($sql, $params = [], $types = '') {
        $result = $this->query($sql, $params, $types);
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
    }
    
    /**
     * Fetch all rows from the database
     * 
     * @param string $sql The SQL query
     * @param array $params The parameters for the query
     * @param string $types The types of the parameters
     * @return array The fetched rows
     */
    public function fetchAll($sql, $params = [], $types = '') {
        $result = $this->query($sql, $params, $types);
        $rows = [];
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $rows[] = $row;
            }
        }
        
        return $rows;
    }
    
    /**
     * Insert data into the database
     * 
     * @param string $table The table name
     * @param array $data The data to insert (column => value)
     * @return int The ID of the inserted row
     */
    public function insert($table, $data) {
        $columns = array_keys($data);
        $values = array_values($data);
        $placeholders = array_fill(0, count($values), '?');
        
        $sql = "INSERT INTO " . $table . " (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $this->query($sql, $values);
        
        return $this->conn->insert_id;
    }
    
    /**
     * Update data in the database
     * 
     * @param string $table The table name
     * @param array $data The data to update (column => value)
     * @param string $where The WHERE clause
     * @param array $whereParams The parameters for the WHERE clause
     * @return int The number of affected rows
     */
    public function update($table, $data, $where, $whereParams = []) {
        $columns = array_keys($data);
        $values = array_values($data);
        
        $set = [];
        foreach ($columns as $column) {
            $set[] = $column . ' = ?';
        }
        
        $sql = "UPDATE " . $table . " SET " . implode(', ', $set) . " WHERE " . $where;
        
        // Combine data values and where params
        $params = array_merge($values, $whereParams);
        
        $this->query($sql, $params);
        
        return $this->conn->affected_rows;
    }
    
    /**
     * Delete data from the database
     * 
     * @param string $table The table name
     * @param string $where The WHERE clause
     * @param array $params The parameters for the WHERE clause
     * @return int The number of affected rows
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM " . $table . " WHERE " . $where;
        
        $this->query($sql, $params);
        
        return $this->conn->affected_rows;
    }
    
    /**
     * Generate parameter types string for bind_param
     * 
     * @param array $params The parameters
     * @return string The types string (e.g., "ssi" for string, string, integer)
     */
    private function generateTypes($params) {
        $types = '';
        
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i';
            } elseif (is_float($param)) {
                $types .= 'd';
            } elseif (is_string($param)) {
                $types .= 's';
            } else {
                $types .= 'b'; // Blob
            }
        }
        
        return $types;
    }
    
    /**
     * Close the database connection
     */
    public function close() {
        if ($this->conn) {
            $this->conn->close();
        }
    }
    
    /**
     * Destructor - closes the database connection
     */
    public function __destruct() {
        $this->close();
    }
}
