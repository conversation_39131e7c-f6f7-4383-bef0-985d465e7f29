<?php
/**
 * Initialization File
 * 
 * This file initializes the application and loads all required files
 */

// Define secure access constant
define('SECURE_ACCESS', true);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Define base path and URL
define('BASE_PATH', dirname(__DIR__));
define('BASE_URL', '//' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']));

// Load configuration
require_once BASE_PATH . '/config.php';

// Load classes
require_once BASE_PATH . '/includes/classes/Database.php';
require_once BASE_PATH . '/includes/classes/Authentication.php';

// Load functions
require_once BASE_PATH . '/includes/functions/utilities.php';

// Initialize database connection
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Initialize authentication
$auth = Authentication::getInstance();

/**
 * Load a template file
 * 
 * @param string $template The template name (without .php extension)
 * @param array $data Variables to pass to the template
 * @return void
 */
function load_template($template, $data = []) {
    // Extract variables from data array
    extract($data);
    
    // Include the template file
    include BASE_PATH . '/includes/templates/' . $template . '.php';
}

/**
 * Load a page with header and footer
 * 
 * @param string $content The content template name
 * @param array $data Variables to pass to the template
 * @param array $options Additional options (title, description, css, js)
 * @return void
 */
function load_page($content, $data = [], $options = []) {
    // Set default options
    $default_options = [
        'title' => APP_NAME,
        'description' => APP_NAME,
        'additional_css' => [],
        'additional_js' => [],
        'page_script' => null
    ];
    
    // Merge options
    $options = array_merge($default_options, $options);
    
    // Set page variables
    $page_title = $options['title'];
    $page_description = $options['description'];
    $additional_css = $options['additional_css'];
    $additional_js = $options['additional_js'];
    $page_script = $options['page_script'];
    
    // Load header
    load_template('header', compact('page_title', 'page_description', 'additional_css'));
    
    // Load content
    load_template($content, $data);
    
    // Load footer
    load_template('footer', compact('additional_js', 'page_script'));
}

/**
 * Check if a user is logged in and has the required role
 * 
 * @param string|array $roles The required role(s)
 * @param string $redirect_url The URL to redirect to if not authorized
 * @return void
 */
function require_role($roles, $redirect_url = 'login.php') {
    global $auth;
    $auth->requireRole($roles, $redirect_url);
}

/**
 * Check if a user is logged in
 * 
 * @param string $redirect_url The URL to redirect to if not logged in
 * @return void
 */
function require_login($redirect_url = 'login.php') {
    global $auth;
    $auth->requireLogin($redirect_url);
}

/**
 * Get the current user's information
 * 
 * @return array|null The user information or null if not logged in
 */
function get_current_user() {
    global $auth;
    return $auth->getUserInfo();
}

/**
 * Get the current user's role
 * 
 * @return string|null The role or null if not logged in
 */
function get_current_role() {
    global $auth;
    return $auth->getRole();
}

/**
 * Check if the current user has a specific role
 * 
 * @param string|array $roles The role(s) to check
 * @return bool True if the user has the role, false otherwise
 */
function has_role($roles) {
    global $auth;
    return $auth->hasRole($roles);
}

/**
 * Check if a user is logged in
 * 
 * @return bool True if logged in, false otherwise
 */
function is_logged_in() {
    global $auth;
    return $auth->isLoggedIn();
}
