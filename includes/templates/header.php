<?php
/**
 * Header Template
 * 
 * This file contains the header for all pages
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS')) {
    header('HTTP/1.1 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);

// Get authentication instance
$auth = Authentication::getInstance();
$isLoggedIn = $auth->isLoggedIn();
$userRole = $auth->getRole();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : APP_NAME; ?>">
    
    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL; ?>/assets/img/logo.webp" type="image/webp">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css">
    
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo BASE_URL . $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <title><?php echo isset($page_title) ? $page_title . ' - ' . APP_NAME : APP_NAME; ?></title>
</head>
<body>
    <!-- Navigation Bar -->
    <nav>
        <img src="<?php echo BASE_URL; ?>/assets/img/logo.webp" alt="DBTI Logo" class="logo">
        <div class="heading"><?php echo APP_NAME; ?></div>
        
        <!-- Hamburger Menu for Mobile -->
        <div class="hamburger" onclick="toggleNavbar()">
            <div></div>
            <div></div>
            <div></div>
        </div>
        
        <!-- Navigation Links -->
        <ul id="navbar">
            <li><a href="<?php echo BASE_URL; ?>/index.php" <?php echo ($current_page == 'index.php') ? 'class="active"' : ''; ?>>Home</a></li>
            <li><a href="<?php echo BASE_URL; ?>/about.php" <?php echo ($current_page == 'about.php') ? 'class="active"' : ''; ?>>About</a></li>
            
            <?php if ($isLoggedIn): ?>
                <?php if ($userRole == 'admin'): ?>
                    <li><a href="<?php echo BASE_URL; ?>/admin_dashboard.php" <?php echo ($current_page == 'admin_dashboard.php') ? 'class="active"' : ''; ?>>Dashboard</a></li>
                <?php elseif ($userRole == 'student'): ?>
                    <li><a href="<?php echo BASE_URL; ?>/student_dashboard.php" <?php echo ($current_page == 'student_dashboard.php') ? 'class="active"' : ''; ?>>Dashboard</a></li>
                <?php elseif ($userRole == 'registrar'): ?>
                    <li><a href="<?php echo BASE_URL; ?>/registrar_dashboard.php" <?php echo ($current_page == 'registrar_dashboard.php') ? 'class="active"' : ''; ?>>Dashboard</a></li>
                <?php elseif ($userRole == 'cashier'): ?>
                    <li><a href="<?php echo BASE_URL; ?>/cashier_dashboard.php" <?php echo ($current_page == 'cashier_dashboard.php') ? 'class="active"' : ''; ?>>Dashboard</a></li>
                <?php endif; ?>
                <li><a href="<?php echo BASE_URL; ?>/logout.php">Logout <i class="fa-solid fa-user"></i></a></li>
            <?php else: ?>
                <li><a href="<?php echo BASE_URL; ?>/login.php" <?php echo ($current_page == 'login.php') ? 'class="active"' : ''; ?>>Login <i class="fa-solid fa-user"></i></a></li>
            <?php endif; ?>
        </ul>
    </nav>
    
    <!-- Side Navigation Bar for Mobile -->
    <div class="sideNavigationBar" id="sideNavigationBar">
        <a href="javascript:void(0)" class="closeButton" onclick="closeNavbar()">&#x274C;</a>
        <a href="<?php echo BASE_URL; ?>/index.php" <?php echo ($current_page == 'index.php') ? 'class="active"' : ''; ?>>Home</a>
        <a href="<?php echo BASE_URL; ?>/about.php" <?php echo ($current_page == 'about.php') ? 'class="active"' : ''; ?>>About</a>
        
        <?php if ($isLoggedIn): ?>
            <?php if ($userRole == 'admin'): ?>
                <a href="<?php echo BASE_URL; ?>/admin_dashboard.php" <?php echo ($current_page == 'admin_dashboard.php') ? 'class="active"' : ''; ?>>Dashboard</a>
            <?php elseif ($userRole == 'student'): ?>
                <a href="<?php echo BASE_URL; ?>/student_dashboard.php" <?php echo ($current_page == 'student_dashboard.php') ? 'class="active"' : ''; ?>>Dashboard</a>
            <?php elseif ($userRole == 'registrar'): ?>
                <a href="<?php echo BASE_URL; ?>/registrar_dashboard.php" <?php echo ($current_page == 'registrar_dashboard.php') ? 'class="active"' : ''; ?>>Dashboard</a>
            <?php elseif ($userRole == 'cashier'): ?>
                <a href="<?php echo BASE_URL; ?>/cashier_dashboard.php" <?php echo ($current_page == 'cashier_dashboard.php') ? 'class="active"' : ''; ?>>Dashboard</a>
            <?php endif; ?>
            <a href="<?php echo BASE_URL; ?>/logout.php">Logout</a>
        <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/login.php" <?php echo ($current_page == 'login.php') ? 'class="active"' : ''; ?>>Login</a>
        <?php endif; ?>
    </div>
    
    <!-- Display flash messages if any -->
    <?php if (isset($_SESSION['message'])): ?>
        <div class="alert alert-info">
            <?php 
                echo $_SESSION['message']; 
                unset($_SESSION['message']); // Clear the message after displaying
            ?>
        </div>
    <?php endif; ?>
    
    <!-- Main Content Container -->
    <div class="container">
