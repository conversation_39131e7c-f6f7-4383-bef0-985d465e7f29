<div class="dashboard-container">
    <!-- Sidebar -->
    <div class="sidebar">
        <a href="<?php echo BASE_URL; ?>/student_dashboard.php" class="<?php echo ($current_page == 'student_dashboard.php') ? 'active' : ''; ?>">
            <i class="fas fa-home"></i> Dashboard
        </a>
        <a href="<?php echo BASE_URL; ?>/course_registration.php" class="<?php echo ($current_page == 'course_registration.php') ? 'active' : ''; ?>">
            <i class="fas fa-book"></i> Course Registration
        </a>
        <a href="<?php echo BASE_URL; ?>/payment.php" class="<?php echo ($current_page == 'payment.php') ? 'active' : ''; ?>">
            <i class="fas fa-credit-card"></i> Make Payment
        </a>
        <a href="<?php echo BASE_URL; ?>/payment_history.php" class="<?php echo ($current_page == 'payment_history.php') ? 'active' : ''; ?>">
            <i class="fas fa-history"></i> Payment History
        </a>
        <a href="<?php echo BASE_URL; ?>/profile.php" class="<?php echo ($current_page == 'profile.php') ? 'active' : ''; ?>">
            <i class="fas fa-user"></i> Profile
        </a>
        <hr>
        <a href="<?php echo BASE_URL; ?>/logout.php">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <h1>Welcome, <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h1>
        
        <!-- Student Information Card -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-user"></i> Student Information</h3>
            </div>
            <div class="card-body">
                <div class="student-info">
                    <div class="info-item">
                        <span class="label">Student ID:</span>
                        <span class="value"><?php echo htmlspecialchars($student['student_id']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">Email:</span>
                        <span class="value"><?php echo htmlspecialchars($student['email']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">Program:</span>
                        <span class="value"><?php echo htmlspecialchars($student['program']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">Technology:</span>
                        <span class="value"><?php echo htmlspecialchars($student['technology']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">Year Level:</span>
                        <span class="value"><?php echo htmlspecialchars($student['year_level']); ?></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Registration Status Card -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-clipboard-check"></i> Registration Status</h3>
            </div>
            <div class="card-body">
                <div class="status-container">
                    <div class="status-item <?php echo $registration_status ? 'completed' : ''; ?>">
                        <div class="status-icon">
                            <i class="fas <?php echo $registration_status ? 'fa-check-circle' : 'fa-circle'; ?>"></i>
                        </div>
                        <div class="status-text">
                            <h4>Course Registration</h4>
                            <p><?php echo $registration_status ? 'Completed' : 'Not Completed'; ?></p>
                        </div>
                    </div>
                    
                    <div class="status-item <?php echo $payment_status ? 'completed' : ''; ?>">
                        <div class="status-icon">
                            <i class="fas <?php echo $payment_status ? 'fa-check-circle' : 'fa-circle'; ?>"></i>
                        </div>
                        <div class="status-text">
                            <h4>Payment</h4>
                            <p><?php echo $payment_status ? 'Completed' : 'Not Completed'; ?></p>
                        </div>
                    </div>
                    
                    <div class="status-item <?php echo $approval_status ? 'completed' : ''; ?>">
                        <div class="status-icon">
                            <i class="fas <?php echo $approval_status ? 'fa-check-circle' : 'fa-circle'; ?>"></i>
                        </div>
                        <div class="status-text">
                            <h4>Registrar Approval</h4>
                            <p><?php echo $approval_status ? 'Approved' : 'Pending'; ?></p>
                        </div>
                    </div>
                </div>
                
                <?php if (!$registration_status): ?>
                    <div class="action-button">
                        <a href="<?php echo BASE_URL; ?>/course_registration.php" class="btn">Register for Courses</a>
                    </div>
                <?php elseif (!$payment_status): ?>
                    <div class="action-button">
                        <a href="<?php echo BASE_URL; ?>/payment.php" class="btn">Make Payment</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Registered Courses Card -->
        <?php if ($registration_status): ?>
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-book"></i> Registered Courses</h3>
            </div>
            <div class="card-body">
                <?php if (empty($courses)): ?>
                    <p>No courses registered yet.</p>
                <?php else: ?>
                    <table>
                        <thead>
                            <tr>
                                <th>Course ID</th>
                                <th>Course Name</th>
                                <th>Credits</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($courses as $course): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($course['course_id']); ?></td>
                                    <td><?php echo htmlspecialchars($course['course_name']); ?></td>
                                    <td><?php echo htmlspecialchars($course['credits']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    
                    <div class="action-button">
                        <a href="<?php echo BASE_URL; ?>/download_pdf.php" class="btn">Download Registration PDF</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Payment Summary Card -->
        <?php if ($payment_status): ?>
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-money-bill-wave"></i> Payment Summary</h3>
            </div>
            <div class="card-body">
                <div class="payment-summary">
                    <div class="info-item">
                        <span class="label">Total Fees:</span>
                        <span class="value"><?php echo format_currency($total_fees); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">Amount Paid:</span>
                        <span class="value"><?php echo format_currency($amount_paid); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">Balance:</span>
                        <span class="value"><?php echo format_currency($balance); ?></span>
                    </div>
                </div>
                
                <?php if ($balance > 0): ?>
                    <div class="action-button">
                        <a href="<?php echo BASE_URL; ?>/payment.php" class="btn">Pay Balance</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
