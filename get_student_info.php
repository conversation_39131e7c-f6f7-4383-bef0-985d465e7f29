<?php
// Start session before including any files that might try to modify session settings
session_start();

// Define secure access constant for config.php
define('SECURE_ACCESS', true);

// Include configuration and database connection
require_once 'config.php';
require_once 'db_conn.php';

if (!isset($_SESSION['username'])) {
    http_response_code(401);
    exit('Unauthorized');
}

$stmt = $conn->prepare("SELECT * FROM students WHERE student_id = ?");
$stmt->bind_param("s", $_SESSION['username']);
$stmt->execute();
$result = $stmt->get_result();
$student_data = $result->fetch_assoc();

header('Content-Type: application/json');
echo json_encode($student_data);

$stmt->close();
$conn->close();
