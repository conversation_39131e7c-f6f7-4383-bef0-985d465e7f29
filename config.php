<?php
/**
 * Configuration file for DBTI Online Registration System
 * This file contains all configuration settings for the application
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS') && basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    header('HTTP/1.1 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

// Define a constant to indicate this file has been included
define('SECURE_ACCESS', true);

// Security Settings
define('CSRF_TOKEN_NAME', 'dbti_csrf_token');
define('SESSION_LIFETIME', 3600); // 1 hour in seconds
define('PASSWORD_MIN_LENGTH', 8);

// Check if session is already started
if (session_status() === PHP_SESSION_NONE) {
    // Set secure session parameters before starting the session
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.cookie_samesite', 'Strict');
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
}

// Load environment variables from .env file if it exists and if php-dotenv is available
if (file_exists(__DIR__ . '/.env') && class_exists('Dotenv\Dotenv')) {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
}

// Include security helper functions
require_once __DIR__ . '/security_helpers.php';

// Database Configuration
// Try to get from environment variables first, fall back to defaults if not available
define('DB_HOST', getenv('DB_HOST') ?: 'localhost');
define('DB_USER', getenv('DB_USER') ?: 'u787474055_dbtionline');
define('DB_PASSWORD', getenv('DB_PASSWORD') ?: 'Blackpanther707@707');
define('DB_NAME', getenv('DB_NAME') ?: 'u787474055_dbtionline');

// Email Configuration
define('SMTP_HOST', getenv('SMTP_HOST') ?: 'smtp.hostinger.com');
define('SMTP_PORT', getenv('SMTP_PORT') ?: 465);
define('SMTP_SECURE', getenv('SMTP_SECURE') ?: 'ssl');
define('SMTP_AUTH', getenv('SMTP_AUTH') ?: true);

// Email accounts
define('REGISTRAR_EMAIL', getenv('REGISTRAR_EMAIL') ?: '<EMAIL>');
define('REGISTRAR_PASSWORD', getenv('REGISTRAR_PASSWORD') ?: 'Blackpanther707@');
define('CASHIER_EMAIL', getenv('CASHIER_EMAIL') ?: '<EMAIL>');
define('CASHIER_PASSWORD', getenv('CASHIER_PASSWORD') ?: 'Blackpanther707@707');

// Application Settings
define('APP_NAME', 'DBTI Online Registration');
define('APP_URL', getenv('APP_URL') ?: 'https://dbtionline.waghitech.com');
define('UPLOAD_DIR', __DIR__ . '/uploads/');
define('TEMP_DIR', __DIR__ . '/temp/');

// Stripe Configuration
define('STRIPE_SECRET_KEY', getenv('STRIPE_SECRET_KEY') ?: 'sk_test_51OpK5NKnyip3i5w1YlQZg2XjdXWjIQ0UdTIXZHtkY1MoipKjhvMPurDOpSnf2v6fODGX1P5G9LWUSdafTr0rWsXy00U04R8JIH');
define('STRIPE_PUBLISHABLE_KEY', getenv('STRIPE_PUBLISHABLE_KEY') ?: 'pk_test_51OpK5NKnyip3i5w1gad6XcasjQlbwMsov86qhLdkmrL8vuKmqVxCzjXFtFbH9vjIDa75flwKllwmU37FsKItXkgB008kcsU8wY');
define('STRIPE_WEBHOOK_SECRET', getenv('STRIPE_WEBHOOK_SECRET') ?: 'whsec_9eOmdqyyzJxUcaxFXRpM7JRqAxF4cLaw');

/**
 * Generate a CSRF token
 * @return string The generated token
 */
function generate_csrf_token() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Validate a CSRF token
 * @param string $token The token to validate
 * @return bool True if valid, false otherwise
 */
function validate_csrf_token($token) {
    if (!isset($_SESSION[CSRF_TOKEN_NAME]) || $token !== $_SESSION[CSRF_TOKEN_NAME]) {
        return false;
    }
    return true;
}

/**
 * Generate a random filename with the given extension
 * @param string $extension File extension (without dot)
 * @return string Random filename with extension
 */
function generate_secure_filename($extension) {
    return bin2hex(random_bytes(16)) . '.' . $extension;
}

/**
 * Sanitize user input
 * @param string $input The input to sanitize
 * @return string Sanitized input
 */
function sanitize_input($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Create a .env.example file if it doesn't exist
if (!file_exists(__DIR__ . '/.env.example')) {
    $envExample = <<<EOT
# Database Configuration
DB_HOST=localhost
DB_USER=database_username
DB_PASSWORD=database_password
DB_NAME=database_name

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=465
SMTP_SECURE=ssl
SMTP_AUTH=true
REGISTRAR_EMAIL=<EMAIL>
REGISTRAR_PASSWORD=email_password
CASHIER_EMAIL=<EMAIL>
CASHIER_PASSWORD=email_password

# Application Settings
APP_URL=https://example.com

# Stripe Configuration
STRIPE_WEBHOOK_SECRET=your_webhook_secret
EOT;

    file_put_contents(__DIR__ . '/.env.example', $envExample);
}

// Create a .gitignore file if it doesn't exist
if (!file_exists(__DIR__ . '/.gitignore') && is_writable(__DIR__)) {
    $gitignore = <<<EOT
# Environment variables
.env

# Temporary files
/temp/*
!/temp/.gitkeep

# Uploaded files
/uploads/*
!/uploads/.gitkeep

# Vendor directory
/vendor/

# Logs
/logs/*
!/logs/.gitkeep

# Cache
/cache/*
!/cache/.gitkeep

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
EOT;

    file_put_contents(__DIR__ . '/.gitignore', $gitignore);
}

// Create necessary directories
$directories = [UPLOAD_DIR, TEMP_DIR, __DIR__ . '/logs/', __DIR__ . '/cache/'];
foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
        file_put_contents($dir . '.gitkeep', '');
    }
}
