<?php
// Define secure access constant
define('SECURE_ACCESS', true);

session_start();
require_once 'config.php';

// Generate CSRF token for the form
$csrf_token = generate_csrf_token();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Login - DBTI Online Registration">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
    <link rel="icon" href="img/logo.webp" type="image/png">
    <title>Login - DBTI Online Registration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            display: flex;
            flex-direction: column;
            font-family: Arial, sans-serif;
            height: 100%;
            line-height: 1.6;
            margin-bottom: 80px;
            font-family: 'Inter', sans-serif;
            color: #333;
            background-color: #f5f5f5;
            scroll-behavior: smooth;
        }

        nav {
            background-color: #ffbf00;
            padding: 20px 5%;
            position: sticky;
            top: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            text-align: center;
            box-shadow: 0px 4px 8px rgba(0,0,0,0.1);
        }

        nav img {
            height: 50px;
        }

        nav .heading {
            font-size: 28px;
            font-weight: bold;
            color: #fff;
            transition: transform 0.3s ease;
        }

        .navbar ul {
            list-style: none;
            display: flex;
            gap: 30px;
        }

        nav ul li a {
            font-size: 16px;
            color: #fff;
            text-transform: uppercase;
            padding: 10px 15px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        nav ul li a:hover {
            background-color: green;
            border-radius: 5px;
            color: #fff;
            transform: scale(1.1);
        }

        footer {
            background-color: #2d3748;
            color: #ecf0f1;
            padding: 40px 5%;
            text-align: center;
            position: fixed;
            bottom: 0;
            width: 100%; /* Make sure the footer stretches across the full width */
            height: 60px; /* Set a fixed height */
            z-index: 1000; /* Ensure it's on top of other elements */
        }

        footer a {
            text-decoration: underline;
            color: #e74c3c;
            text-decoration: none;
            font-weight: bold;
        }

        footer a:hover {
            text-decoration: underline;
        }

        /* Hamburger Menu */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 4px 0;
            transition: 0.4s;
        }

        /* Responsive Styles */
        @media only screen and (max-width: 768px) {
            nav {
                flex-wrap: wrap;
            }

            nav .logo {
                height: 40px;
            }

            nav ul {
                display: none;
                flex-direction: column;
                width: 100%;
                text-align: center;
                background-color: #ffbf00;
            }

            nav ul.active {
                display: flex;
            }

            nav ul li a {
                width: 200px;
                margin: 8px auto;
                text-align: center;
                display: block;
            }

            .hamburger {
                display: flex;
            }

            nav .heading {
                font-size: 1.2rem;
            }
        }
    </style>

    <script>
        function toggleNavbar() {
            const navbar = document.getElementById('navbar');
            navbar.classList.toggle('active');
        }
    </script>
</head>
<body>
    <nav>
        <img src="img/logo.webp" alt="DBTI Logo" class="logo">
        <div class="heading">DBTI Online Registration</div>
        <div class="hamburger" onclick="toggleNavbar()">
            <div></div>
            <div></div>
            <div></div>
        </div>
        <ul id="navbar">
            <li><a href="index.php">Home</a></li>
            <li><a href="about.php">About</a></li>
            <li><a href="admin_dashboard.php">Dashboard</a></li>
            <li><a href="login.php">Login</a></li>
        </ul>
    </nav>

    <br>

    <section class="login-section">
        <div class="login-container">
            <h2>Login</h2>
            <form action="authenticate.php" method="post">
                <!-- CSRF Protection -->
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                <?php if (isset($_GET['error'])): ?>
                <div class="error-message">
                    <?php
                    $error = $_GET['error'];
                    if ($error === 'invalid_token') {
                        echo 'Security validation failed. Please try again.';
                    } elseif ($error === 'invalid_request') {
                        echo 'Invalid request. Please try again.';
                    } elseif ($error === 'invalid_credentials') {
                        echo 'Invalid username or password.';
                    } else {
                        echo 'An error occurred. Please try again.';
                    }
                    ?>
                </div>
                <?php endif; ?>

                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
                <button type="submit">Login</button>
            </form>
        </div>
    </section>

    <!-- Footer section -->
    <footer>
    <div class="footerlogin">
        <span>
            Copyright © 2024 Don Bosco Technological Institute. All Rights Reserved.
            <a href="https://www.dbti.ac.pg/" target="_blank">DBTI Website</a>
        </span>
    </div>
    </footer>
    <script src="script.js"></script>
    <style>
        nav {
            background-color: #ffbf00;
            padding: 20px 5%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
        }

        nav .logo {
            height: 50px;
            width: auto;
            margin-right: 15px;
        }

        nav ul li a {
            font-size: 1rem;
            color: #fff;
            text-transform: uppercase;
            padding: 12px 20px;
            background-color: #cc9900;
            border-radius: 6px;
            font-weight: 700;
            transition: all 0.3s ease;
            display: inline-block;
        }

        nav ul li a:hover {
            background-color: #996600;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* Footer update */
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 15px 0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        footer a {
            color: #ffbf00;
            text-decoration: none;
        }

        footer a:hover {
            text-decoration: underline;
        }



        .login-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 180px);
    padding: 20px;
}

.login-container {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    transition: transform 0.3s ease;
}

.login-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.login-container h2 {
    text-align: center;
    color: #333;
    font-size: 2rem;
    margin-bottom: 30px;
}

.login-container form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.login-container label {
    font-weight: 600;
    color: #555;
}

.login-container input {
    padding: 12px 15px;
    border: 2px solid #eee;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.login-container input:focus {
    border-color: #ffbf00;
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 191, 0, 0.1);
}

.login-container button {
    background: Orangered;
    color: white;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-container button:hover {
    background: #e6ac00;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 191, 0, 0.3);
}

@media screen and (max-width: 768px) {
    .login-container {
        padding: 30px 20px;
        margin: 20px;
    }

    .login-container h2 {
        font-size: 1.8rem;
    }
}







    </style>
    <script>
        function toggleNavbar() {
            const navbar = document.getElementById('navbar');
            navbar.classList.toggle('active');
        }
    </script>
</body>
</html>
