<?php
/**
 * Security Helper Functions
 * This file contains functions to enhance security throughout the application
 */

// Prevent direct access to this file
if (!defined('SECURE_ACCESS') && basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    header('HTTP/1.1 403 Forbidden');
    exit('Direct access to this file is forbidden.');
}

/**
 * Check if the current session has expired
 * @return bool True if session has expired, false otherwise
 */
function session_expired() {
    $max_lifetime = SESSION_LIFETIME;
    
    if (!isset($_SESSION['last_activity'])) {
        return true;
    }
    
    if ((time() - $_SESSION['last_activity']) > $max_lifetime) {
        return true;
    }
    
    // Update last activity time
    $_SESSION['last_activity'] = time();
    return false;
}

/**
 * Validate file upload
 * @param array $file The $_FILES array element
 * @param array $allowed_types Array of allowed MIME types
 * @param int $max_size Maximum file size in bytes
 * @return array Result with status and message
 */
function validate_file_upload($file, $allowed_types, $max_size = 5242880) {
    $result = [
        'status' => false,
        'message' => '',
        'filename' => ''
    ];
    
    // Check if file was uploaded properly
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        $result['message'] = 'File upload failed: ';
        switch ($file['error']) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $result['message'] .= 'File is too large.';
                break;
            case UPLOAD_ERR_PARTIAL:
                $result['message'] .= 'File was only partially uploaded.';
                break;
            case UPLOAD_ERR_NO_FILE:
                $result['message'] .= 'No file was uploaded.';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $result['message'] .= 'Missing temporary folder.';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $result['message'] .= 'Failed to write file to disk.';
                break;
            case UPLOAD_ERR_EXTENSION:
                $result['message'] .= 'A PHP extension stopped the file upload.';
                break;
            default:
                $result['message'] .= 'Unknown error.';
        }
        return $result;
    }
    
    // Check file size
    if ($file['size'] > $max_size) {
        $result['message'] = 'File is too large. Maximum size is ' . ($max_size / 1024 / 1024) . 'MB.';
        return $result;
    }
    
    // Check file type
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $file_type = $finfo->file($file['tmp_name']);
    
    if (!in_array($file_type, $allowed_types)) {
        $result['message'] = 'Invalid file type. Allowed types: ' . implode(', ', $allowed_types);
        return $result;
    }
    
    // Generate a secure filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $new_filename = generate_secure_filename($extension);
    
    // Move the file to the upload directory
    if (!move_uploaded_file($file['tmp_name'], UPLOAD_DIR . $new_filename)) {
        $result['message'] = 'Failed to save the uploaded file.';
        return $result;
    }
    
    $result['status'] = true;
    $result['message'] = 'File uploaded successfully.';
    $result['filename'] = $new_filename;
    
    return $result;
}

/**
 * Check if a request is an AJAX request
 * @return bool True if AJAX request, false otherwise
 */
function is_ajax_request() {
    return (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest');
}

/**
 * Validate email address
 * @param string $email Email address to validate
 * @return bool True if valid, false otherwise
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (basic validation)
 * @param string $phone Phone number to validate
 * @return bool True if valid, false otherwise
 */
function is_valid_phone($phone) {
    // Remove non-digit characters
    $digits_only = preg_replace('/\D/', '', $phone);
    // Check if it has at least 7 digits
    return strlen($digits_only) >= 7;
}

/**
 * Check if password meets minimum requirements
 * @param string $password Password to check
 * @return array Result with status and message
 */
function validate_password($password) {
    $result = [
        'status' => true,
        'message' => 'Password is valid.'
    ];
    
    // Check length
    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        $result['status'] = false;
        $result['message'] = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
        return $result;
    }
    
    // Check for at least one uppercase letter
    if (!preg_match('/[A-Z]/', $password)) {
        $result['status'] = false;
        $result['message'] = 'Password must contain at least one uppercase letter.';
        return $result;
    }
    
    // Check for at least one lowercase letter
    if (!preg_match('/[a-z]/', $password)) {
        $result['status'] = false;
        $result['message'] = 'Password must contain at least one lowercase letter.';
        return $result;
    }
    
    // Check for at least one number
    if (!preg_match('/[0-9]/', $password)) {
        $result['status'] = false;
        $result['message'] = 'Password must contain at least one number.';
        return $result;
    }
    
    return $result;
}

/**
 * Log security events
 * @param string $event Description of the event
 * @param string $level Log level (info, warning, error)
 * @return void
 */
function security_log($event, $level = 'info') {
    $log_file = __DIR__ . '/logs/security.log';
    $timestamp = date('Y-m-d H:i:s');
    $user = isset($_SESSION['username']) ? $_SESSION['username'] : 'guest';
    $ip = $_SERVER['REMOTE_ADDR'];
    
    $log_entry = "[$timestamp] [$level] [$user] [$ip] $event" . PHP_EOL;
    
    // Ensure log directory exists
    if (!file_exists(dirname($log_file))) {
        mkdir(dirname($log_file), 0755, true);
    }
    
    file_put_contents($log_file, $log_entry, FILE_APPEND);
}
