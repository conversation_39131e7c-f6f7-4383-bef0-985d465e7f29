<?php
/**
 * Student Dashboard
 * 
 * This is the main dashboard for student users
 */

// Load initialization file
require_once 'includes/init.php';

// Require student role
require_role('student');

// Get student information
$db = Database::getInstance();
$student_id = $_SESSION['username'];

// Get student details
$student = get_student_info($student_id);

if (!$student) {
    set_flash_message('Student information not found.', 'danger');
    redirect('logout.php');
}

// Get registration status
$registration_status = false;
$courses = get_student_courses($student_id, get_current_academic_year(), get_current_semester());
if (!empty($courses)) {
    $registration_status = true;
}

// Get payment status
$payment_status = false;
$total_fees = 0;
$amount_paid = 0;
$balance = 0;

// Calculate total fees based on courses
foreach ($courses as $course) {
    $total_fees += $course['credits'] * 100; // Assuming 100 per credit
}

// Get payment information
$sql = "SELECT SUM(amount) as total_paid FROM payments WHERE student_id = ?";
$payment = $db->fetchRow($sql, [$student_id]);
$amount_paid = $payment['total_paid'] ?? 0;

// Calculate balance
$balance = $total_fees - $amount_paid;

// Payment status is true if balance is 0 or negative
$payment_status = ($balance <= 0);

// Get approval status
$approval_status = false;
$sql = "SELECT approved FROM registrations WHERE student_id = ? AND academic_year = ? AND semester = ? LIMIT 1";
$approval = $db->fetchRow($sql, [$student_id, get_current_academic_year(), get_current_semester()]);
$approval_status = ($approval && $approval['approved'] == 1);

// Set page options
$options = [
    'title' => 'Student Dashboard - ' . APP_NAME,
    'description' => 'Manage your courses, payments, and academic information.',
    'additional_css' => [
        '/assets/css/dashboard.css'
    ],
    'additional_js' => [
        '/assets/js/dashboard.js'
    ]
];

// Load the page with the student dashboard template
load_page('student_dashboard', compact(
    'student', 
    'courses', 
    'registration_status', 
    'payment_status', 
    'approval_status',
    'total_fees',
    'amount_paid',
    'balance'
), $options);
