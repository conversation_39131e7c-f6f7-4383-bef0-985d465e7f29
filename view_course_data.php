<?php
session_start();
require_once 'db_conn.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is admin, or allow any user for testing purposes
$is_admin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Data Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Course Data Viewer</h1>
    
    <div class="section">
        <h2>Programs in Database</h2>
        <?php
        // Get all programs
        $program_sql = "SELECT DISTINCT program FROM courses ORDER BY program";
        $program_result = $conn->query($program_sql);
        
        if ($program_result && $program_result->num_rows > 0) {
            echo "<table>";
            echo "<tr><th>Program</th><th>Action</th></tr>";
            
            while ($row = $program_result->fetch_assoc()) {
                $program = $row['program'];
                echo "<tr>";
                echo "<td>" . htmlspecialchars($program) . "</td>";
                echo "<td><a href='#" . urlencode($program) . "' onclick='showTechnologiesFor(\"" . htmlspecialchars($program) . "\")'>View Technologies</a></td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p class='error'>No programs found in the database.</p>";
        }
        ?>
    </div>
    
    <div class="section">
        <h2>Technologies by Program</h2>
        <div id="technologies-container">
            <p>Select a program above to view its technologies.</p>
        </div>
    </div>
    
    <div class="section">
        <h2>Year Levels by Program and Technology</h2>
        <div id="years-container">
            <p>Select a technology to view its year levels.</p>
        </div>
    </div>
    
    <div class="section">
        <h2>Raw Course Data</h2>
        <?php
        // Get all courses (limited to 100 for performance)
        $courses_sql = "SELECT * FROM courses ORDER BY program, technology, year_level, semester, course_id LIMIT 100";
        $courses_result = $conn->query($courses_sql);
        
        if ($courses_result && $courses_result->num_rows > 0) {
            echo "<p>Showing up to 100 courses from the database:</p>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Course ID</th><th>Course Name</th><th>Program</th><th>Technology</th><th>Year</th><th>Semester</th></tr>";
            
            while ($row = $courses_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . htmlspecialchars($row['course_id']) . "</td>";
                echo "<td>" . htmlspecialchars($row['course_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['program']) . "</td>";
                echo "<td>" . htmlspecialchars($row['technology']) . "</td>";
                echo "<td>" . htmlspecialchars($row['year_level']) . "</td>";
                echo "<td>" . htmlspecialchars($row['semester']) . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p class='error'>No courses found in the database.</p>";
        }
        ?>
    </div>
    
    <script>
        // Function to show technologies for a specific program
        function showTechnologiesFor(program) {
            const container = document.getElementById('technologies-container');
            container.innerHTML = '<p>Loading technologies for ' + program + '...</p>';
            
            // Use XMLHttpRequest for better compatibility
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'get_technologies.php?program=' + encodeURIComponent(program), true);
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const technologies = JSON.parse(xhr.responseText);
                        
                        if (technologies.length === 0) {
                            container.innerHTML = '<p class="error">No technologies found for program: ' + program + '</p>';
                            return;
                        }
                        
                        let html = '<h3>Technologies for ' + program + '</h3>';
                        html += '<table>';
                        html += '<tr><th>Technology</th><th>Action</th></tr>';
                        
                        technologies.forEach(tech => {
                            html += '<tr>';
                            html += '<td>' + tech + '</td>';
                            html += '<td><a href="#" onclick="showYearsFor(\'' + program + '\', \'' + tech + '\')">View Years</a></td>';
                            html += '</tr>';
                        });
                        
                        html += '</table>';
                        container.innerHTML = html;
                    } catch (e) {
                        container.innerHTML = '<p class="error">Error parsing data: ' + e.message + '</p>';
                    }
                } else {
                    container.innerHTML = '<p class="error">Error loading technologies. Status: ' + xhr.status + '</p>';
                }
            };
            
            xhr.onerror = function() {
                container.innerHTML = '<p class="error">Network error when loading technologies</p>';
            };
            
            xhr.send();
        }
        
        // Function to show years for a specific program and technology
        function showYearsFor(program, tech) {
            const container = document.getElementById('years-container');
            container.innerHTML = '<p>Loading years for ' + program + ' - ' + tech + '...</p>';
            
            // Use XMLHttpRequest for better compatibility
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'get_years.php?program=' + encodeURIComponent(program) + '&technology=' + encodeURIComponent(tech), true);
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const years = JSON.parse(xhr.responseText);
                        
                        if (years.length === 0) {
                            container.innerHTML = '<p class="error">No years found for ' + program + ' - ' + tech + '</p>';
                            return;
                        }
                        
                        let html = '<h3>Years for ' + program + ' - ' + tech + '</h3>';
                        html += '<table>';
                        html += '<tr><th>Year Level</th><th>Action</th></tr>';
                        
                        years.forEach(year => {
                            html += '<tr>';
                            html += '<td>' + year + '</td>';
                            html += '<td><a href="registration.php?program=' + encodeURIComponent(program) + '&tech=' + encodeURIComponent(tech) + '&year_level=' + encodeURIComponent(year) + '">Register for this</a></td>';
                            html += '</tr>';
                        });
                        
                        html += '</table>';
                        container.innerHTML = html;
                    } catch (e) {
                        container.innerHTML = '<p class="error">Error parsing data: ' + e.message + '</p>';
                    }
                } else {
                    container.innerHTML = '<p class="error">Error loading years. Status: ' + xhr.status + '</p>';
                }
            };
            
            xhr.onerror = function() {
                container.innerHTML = '<p class="error">Network error when loading years</p>';
            };
            
            xhr.send();
        }
        
        // Get count of courses by program
        document.addEventListener('DOMContentLoaded', function() {
            // Load the first program's technologies automatically for demonstration
            const firstProgramLink = document.querySelector('a[onclick^="showTechnologiesFor"]');
            if (firstProgramLink) {
                firstProgramLink.click();
            }
        });
    </script>
</body>
</html> 