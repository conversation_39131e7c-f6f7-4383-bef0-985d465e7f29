/**
 * Main JavaScript file for DBTI Online Registration System
 */

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavbar();
    initAlerts();
    initForms();
});

/**
 * Initialize navigation functionality
 */
function initNavbar() {
    // Mobile navigation toggle
    window.toggleNavbar = function() {
        const navbar = document.getElementById('navbar');
        if (navbar) {
            navbar.classList.toggle('active');
        }
    };
    
    // Side navigation bar functions
    window.openNavbar = function() {
        const sideNav = document.getElementById('sideNavigationBar');
        if (sideNav) {
            sideNav.style.width = '250px';
            sideNav.style.display = 'block';
        }
    };
    
    window.closeNavbar = function() {
        const sideNav = document.getElementById('sideNavigationBar');
        if (sideNav) {
            sideNav.style.width = '0';
            setTimeout(function() {
                sideNav.style.display = 'none';
            }, 500);
        }
    };
    
    // Close side navigation when clicking outside
    document.addEventListener('click', function(event) {
        const sideNav = document.getElementById('sideNavigationBar');
        const hamburger = document.querySelector('.hamburger');
        
        if (sideNav && hamburger && 
            event.target !== sideNav && 
            !sideNav.contains(event.target) && 
            event.target !== hamburger && 
            !hamburger.contains(event.target)) {
            closeNavbar();
        }
    });
}

/**
 * Initialize alert messages
 */
function initAlerts() {
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.style.display = 'none';
            }, 500);
        }, 5000);
    });
    
    // Add close button to alerts
    alerts.forEach(function(alert) {
        const closeButton = document.createElement('span');
        closeButton.innerHTML = '&times;';
        closeButton.className = 'alert-close';
        closeButton.style.float = 'right';
        closeButton.style.cursor = 'pointer';
        closeButton.style.fontWeight = 'bold';
        
        closeButton.addEventListener('click', function() {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.style.display = 'none';
            }, 500);
        });
        
        alert.insertBefore(closeButton, alert.firstChild);
    });
}

/**
 * Initialize form functionality
 */
function initForms() {
    // Form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');
                    
                    // Create error message if it doesn't exist
                    let errorMsg = field.nextElementSibling;
                    if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                        errorMsg = document.createElement('div');
                        errorMsg.className = 'error-message';
                        errorMsg.style.color = 'red';
                        errorMsg.style.fontSize = '0.8rem';
                        errorMsg.style.marginTop = '5px';
                        field.parentNode.insertBefore(errorMsg, field.nextSibling);
                    }
                    
                    errorMsg.textContent = 'This field is required';
                } else {
                    field.classList.remove('error');
                    
                    // Remove error message if it exists
                    const errorMsg = field.nextElementSibling;
                    if (errorMsg && errorMsg.classList.contains('error-message')) {
                        errorMsg.textContent = '';
                    }
                }
            });
            
            if (!isValid) {
                event.preventDefault();
            }
        });
        
        // Clear error styling on input
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(function(input) {
            input.addEventListener('input', function() {
                input.classList.remove('error');
                
                // Remove error message if it exists
                const errorMsg = input.nextElementSibling;
                if (errorMsg && errorMsg.classList.contains('error-message')) {
                    errorMsg.textContent = '';
                }
            });
        });
    });
}

/**
 * Create a confirmation dialog
 * 
 * @param {string} message - The confirmation message
 * @param {function} callback - The callback function to execute if confirmed
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Format a number as currency
 * 
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (default: 'K')
 * @returns {string} The formatted currency string
 */
function formatCurrency(amount, currency = 'K') {
    return currency + parseFloat(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
}

/**
 * Fetch data from an API endpoint
 * 
 * @param {string} url - The URL to fetch data from
 * @param {object} options - Fetch options
 * @returns {Promise} A promise that resolves to the fetched data
 */
async function fetchData(url, options = {}) {
    try {
        const response = await fetch(url, options);
        
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}

/**
 * Create a modal dialog
 * 
 * @param {string} title - The modal title
 * @param {string} content - The modal content (HTML)
 * @param {function} onClose - Callback function when modal is closed
 */
function createModal(title, content, onClose = null) {
    // Create modal container
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.style.position = 'fixed';
    modal.style.zIndex = '1000';
    modal.style.left = '0';
    modal.style.top = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.overflow = 'auto';
    modal.style.backgroundColor = 'rgba(0,0,0,0.4)';
    
    // Create modal content
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    modalContent.style.backgroundColor = '#fefefe';
    modalContent.style.margin = '15% auto';
    modalContent.style.padding = '20px';
    modalContent.style.border = '1px solid #888';
    modalContent.style.width = '80%';
    modalContent.style.maxWidth = '600px';
    modalContent.style.borderRadius = '5px';
    modalContent.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
    
    // Create close button
    const closeButton = document.createElement('span');
    closeButton.className = 'close-button';
    closeButton.innerHTML = '&times;';
    closeButton.style.color = '#aaa';
    closeButton.style.float = 'right';
    closeButton.style.fontSize = '28px';
    closeButton.style.fontWeight = 'bold';
    closeButton.style.cursor = 'pointer';
    
    closeButton.addEventListener('mouseover', function() {
        closeButton.style.color = '#000';
    });
    
    closeButton.addEventListener('mouseout', function() {
        closeButton.style.color = '#aaa';
    });
    
    closeButton.addEventListener('click', function() {
        document.body.removeChild(modal);
        if (onClose) onClose();
    });
    
    // Create title
    const modalTitle = document.createElement('h2');
    modalTitle.textContent = title;
    modalTitle.style.marginTop = '0';
    
    // Add content
    const modalBody = document.createElement('div');
    modalBody.innerHTML = content;
    
    // Assemble modal
    modalContent.appendChild(closeButton);
    modalContent.appendChild(modalTitle);
    modalContent.appendChild(modalBody);
    modal.appendChild(modalContent);
    
    // Add modal to body
    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            document.body.removeChild(modal);
            if (onClose) onClose();
        }
    });
    
    return modal;
}
