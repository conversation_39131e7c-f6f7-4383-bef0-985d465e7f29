/* 
 * Main Stylesheet for DBTI Online Registration System
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', Arial, sans-serif;
    color: #333;
    background-color: #f5f5f5;
    scroll-behavior: smooth;
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: #ffbf00;
}

a:hover {
    text-decoration: underline;
}

ul {
    list-style: none;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Navigation Bar */
nav {
    background-color: #ffbf00;
    padding: 20px 5%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
}

nav .logo {
    height: 50px;
    width: auto;
    margin-right: 15px;
}

nav .heading {
    font-size: 1.75rem;
    font-weight: bold;
    color: #fff;
}

nav ul {
    display: flex;
    gap: 20px;
}

nav ul li a {
    font-size: 1rem;
    color: #fff;
    text-transform: uppercase;
    padding: 12px 20px;
    background-color: #cc9900;
    border-radius: 6px;
    font-weight: 700;
    transition: all 0.3s ease;
    display: inline-block;
    text-decoration: none;
}

nav ul li a:hover, nav ul li a.active {
    background-color: #996600;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger div {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    border-radius: 3px;
}

/* Side Navigation Bar */
.sideNavigationBar {
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1001;
    top: 0;
    left: 0;
    background-color: #333;
    overflow-x: hidden;
    transition: 0.5s;
    padding-top: 60px;
    display: none;
}

.sideNavigationBar a {
    padding: 15px 25px;
    text-decoration: none;
    font-size: 1.2rem;
    color: #fff;
    display: block;
    transition: 0.3s;
}

.sideNavigationBar a:hover, .sideNavigationBar a.active {
    background-color: #ffbf00;
    color: #333;
}

.sideNavigationBar .closeButton {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.5rem;
    padding: 5px 10px;
}

/* Sidebar for Dashboards */
.sidebar {
    width: 250px;
    height: calc(100vh - 90px);
    background: white;
    position: fixed;
    left: 0;
    top: 90px;
    padding: 20px;
    box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
    overflow-y: auto;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin-bottom: 5px;
}

.sidebar a i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.sidebar a:hover, .sidebar a.active {
    background: #f0f0f0;
    transform: translateX(5px);
}

.sidebar hr {
    margin: 10px 0;
    border: none;
    border-top: 1px solid #eee;
}

/* Main Content for Dashboard Pages */
.main-content {
    margin-left: 250px;
    padding: 20px;
}

/* Forms */
form {
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    margin: 0 auto;
}

form h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
select,
textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
select:focus,
textarea:focus {
    border-color: #ffbf00;
    outline: none;
}

button, .btn {
    background-color: #ffbf00;
    color: #fff;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
}

button:hover, .btn:hover {
    background-color: #cc9900;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background-color: #ffbf00;
    color: #fff;
    font-weight: 600;
}

tr:hover {
    background-color: #f9f9f9;
}

/* Alerts */
.alert {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    font-weight: 500;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Cards */
.card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.card-header h3 {
    margin: 0;
    color: #333;
}

/* Footer */
footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 15px 0;
    position: relative;
    bottom: 0;
    width: 100%;
    margin-top: 40px;
}

footer a {
    color: #ffbf00;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    nav ul {
        display: none;
    }
    
    nav ul.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 80px;
        left: 0;
        width: 100%;
        background-color: #ffbf00;
        padding: 20px;
        z-index: 999;
    }
    
    .hamburger {
        display: flex;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        top: 0;
        padding: 10px;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    form {
        padding: 20px;
    }
}
