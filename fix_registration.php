<?php
session_start();
require_once 'db_conn.php';

// Enable detailed error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is an admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php");
    exit();
}

// Get student ID
$student_id = $_GET['student_id'] ?? '';

if (empty($student_id)) {
    header("Location: debug_registration.php");
    exit();
}

// Check if student exists
$student_sql = "SELECT * FROM students WHERE student_id = ?";
$student_stmt = $conn->prepare($student_sql);
$student_stmt->bind_param("s", $student_id);
$student_stmt->execute();
$student_result = $student_stmt->get_result();

if ($student_result->num_rows === 0) {
    $_SESSION['error'] = "Student not found with ID: $student_id";
    header("Location: debug_registration.php");
    exit();
}

$student = $student_result->fetch_assoc();
$message = '';
$error = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'fix_status') {
        // Update registration status
        $new_status = $_POST['registration_status'];
        $update_sql = "UPDATE students SET registration_status = ? WHERE student_id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ss", $new_status, $student_id);
        
        if ($update_stmt->execute()) {
            $message = "Registration status updated successfully!";
        } else {
            $error = "Error updating registration status: " . $conn->error;
        }
    } elseif ($action === 'create_registration') {
        // Create a new registration record
        $semester = $_POST['semester'];
        $academic_year = $_POST['academic_year'];
        $registration_date = date('Y-m-d H:i:s');
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Insert into registrations table
            $reg_sql = "INSERT INTO registrations (student_id, registration_date, semester, academic_year, status) 
                        VALUES (?, ?, ?, ?, 'current')";
            $reg_stmt = $conn->prepare($reg_sql);
            $reg_stmt->bind_param("ssss", $student_id, $registration_date, $semester, $academic_year);
            
            if (!$reg_stmt->execute()) {
                throw new Exception("Error inserting registration: " . $reg_stmt->error);
            }
            
            // Set registration status in students table
            $update_sql = "UPDATE students SET registration_status = 'registered' WHERE student_id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("s", $student_id);
            
            if (!$update_stmt->execute()) {
                throw new Exception("Error updating student status: " . $update_stmt->error);
            }
            
            // Commit transaction
            $conn->commit();
            $message = "Manual registration created successfully!";
        } catch (Exception $e) {
            // Rollback on error
            $conn->rollback();
            $error = "Registration creation failed: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Registration</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .panel {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            background-color: #f9f9f9;
        }
        
        .panel h3 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>

<body>
    <!-- Navigation Bar -->
    <nav>
        <div class="logo">
            <img src="dbti.png" alt="DBTI Logo">
            <h2>Don Bosco Technological Institute</h2>
        </div>
        <ul>
            <li><a href="admin_dashboard.php">Dashboard</a></li>
            <li><a href="manage_courses.php">Manage Courses</a></li>
            <li><a href="manage_students.php">Manage Students</a></li>
            <li><a href="debug_registration.php">Debug Tool</a></li>
            <li><a href="logout.php">Logout</a></li>
        </ul>
    </nav>
    
    <div class="container">
        <h1>Fix Registration</h1>
        <p>Student: <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name'] . ' (' . $student['student_id'] . ')'); ?></p>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-success"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <div class="panel">
            <h3>Current Registration Status</h3>
            <p>Registration Status: <strong><?php echo htmlspecialchars($student['registration_status']); ?></strong></p>
            
            <form action="" method="post">
                <input type="hidden" name="action" value="fix_status">
                <div class="form-group">
                    <label for="registration_status">Change Registration Status:</label>
                    <select name="registration_status" id="registration_status">
                        <option value="pending" <?php echo ($student['registration_status'] === 'pending') ? 'selected' : ''; ?>>Pending</option>
                        <option value="registered" <?php echo ($student['registration_status'] === 'registered') ? 'selected' : ''; ?>>Registered</option>
                    </select>
                </div>
                <button type="submit" class="btn">Update Status</button>
            </form>
        </div>
        
        <div class="panel">
            <h3>Create Registration Record</h3>
            <p>This will create a registration record for the student without course selections.</p>
            
            <?php
            // Check if registration already exists
            $reg_check_sql = "SELECT COUNT(*) as count FROM registrations WHERE student_id = ?";
            $reg_check_stmt = $conn->prepare($reg_check_sql);
            $reg_check_stmt->bind_param("s", $student_id);
            $reg_check_stmt->execute();
            $reg_check_result = $reg_check_stmt->get_result();
            $reg_check_data = $reg_check_result->fetch_assoc();
            
            if ($reg_check_data['count'] > 0) {
                echo "<div class='alert alert-danger'>Warning: Student already has " . $reg_check_data['count'] . " registration record(s).</div>";
            }
            ?>
            
            <form action="" method="post">
                <input type="hidden" name="action" value="create_registration">
                <div class="form-group">
                    <label for="semester">Semester:</label>
                    <select name="semester" id="semester" required>
                        <option value="Semester 1">Semester 1</option>
                        <option value="Semester 2">Semester 2</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="academic_year">Academic Year:</label>
                    <input type="text" name="academic_year" id="academic_year" value="<?php echo date('Y'); ?>" required>
                </div>
                <button type="submit" class="btn">Create Registration</button>
            </form>
        </div>
        
        <div class="panel">
            <h3>Verification</h3>
            <p>After making changes, verify the student's registration status using the debug tool.</p>
            <a href="debug_registration.php?student_id=<?php echo $student_id; ?>" class="btn btn-secondary">Go to Debug Tool</a>
        </div>
    </div>
    
    <footer>
        <p>&copy; <?php echo date("Y"); ?> Don Bosco Technological Institute. All Rights Reserved.</p>
    </footer>
</body>
</html> 