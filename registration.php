<?php
session_start();
require_once 'db_conn.php';
include 'course_data.php'; // Directly include course data

// Add error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (!isset($_SESSION['username']) || $_SESSION['role'] !== 'student') {
    header("Location: login.php");
    exit();
}

// Fetch complete student information
$student_sql = "SELECT * FROM students WHERE student_id = ?";
$student_stmt = $conn->prepare($student_sql);
$student_stmt->bind_param("s", $_SESSION['username']);
$student_stmt->execute();
$student_result = $student_stmt->get_result();
$student = $student_result->fetch_assoc();

// Fetch payment information
$payment_sql = "SELECT SUM(amount) as total_paid FROM payments WHERE student_id = ?";
$payment_stmt = $conn->prepare($payment_sql);
$payment_stmt->bind_param("s", $_SESSION['username']);
$payment_stmt->execute();
$payment_result = $payment_stmt->get_result();
$payment_data = $payment_result->fetch_assoc();
$total_paid = $payment_data['total_paid'] ?? 0;
$required_fee = 4000;
$has_paid = ($total_paid >= $required_fee);

// Check if student is already registered
if ($student['registration_status'] === 'registered') {
    $_SESSION['message'] = "You have already registered for this semester. Registration will open again for the next semester.";
    header("Location: student_dashboard.php");
    exit();
}

// Get programs from both sources for comparison
echo "<!-- DEBUGGING PROGRAM SOURCE COMPARISON -->";

// 1. Get programs from database
try {
    $db_programs = [];
    $db_query = "SELECT DISTINCT program FROM courses ORDER BY program";
    $db_result = $conn->query($db_query);
    
    if ($db_result) {
        while ($row = $db_result->fetch_assoc()) {
            $db_programs[] = $row['program'];
        }
    }
    
    echo "<!-- DB Programs (" . count($db_programs) . "): " . implode(", ", $db_programs) . " -->";
} catch (Exception $e) {
    echo "<!-- DB Error: " . $e->getMessage() . " -->";
}

// 2. Get programs from course_data.php
$file_programs = array_keys($courses);
echo "<!-- File Programs (" . count($file_programs) . "): " . implode(", ", $file_programs) . " -->";

// IMPORTANT: Use file data as source of truth for program list
$programs = $file_programs;

// Debug database content
echo "<!-- Database Course Count: ";
$count_query = "SELECT COUNT(*) as total FROM courses";
$count_result = $conn->query($count_query);
$count_data = $count_result->fetch_assoc();
echo $count_data['total'] . " -->";

// Get technologies for each program from course_data.php
function getTechnologiesForProgram($program) {
    global $courses;
    if (isset($courses[$program])) {
        return array_keys($courses[$program]);
    }
    return [];
}

// Get years for program and technology from course_data.php
function getYearsForProgramAndTech($program, $tech) {
    global $courses;
    if (isset($courses[$program][$tech])) {
        return array_keys($courses[$program][$tech]);
    }
    return [];
}

// Check if payment is completed
if (!$has_paid) {
    $_SESSION['error'] = "You need to complete the payment before registering.";
    header("Location: student_dashboard.php");
    exit();
}

// Step 2: Show courses if program, tech and year are selected
$show_courses = false;
$available_courses = [];

if (isset($_POST['step']) && $_POST['step'] == '1') {
    // Coming from Step 1, validate and show Step 2
    if (isset($_POST['program']) && isset($_POST['tech']) && isset($_POST['year_level'])) {
        $show_courses = true;
        $selected_program = $_POST['program'];
        $selected_tech = $_POST['tech'];
        $selected_year = $_POST['year_level'];
        
        // Store all form data in session for persistence
        $_SESSION['registration_data'] = $_POST;
        
        // Fetch courses for the selected program, tech and year
        $courses_sql = "SELECT course_id, course_name, semester FROM courses 
                       WHERE program = ? AND technology = ? AND year_level = ?
                       ORDER BY semester, course_id";
        $courses_stmt = $conn->prepare($courses_sql);
        $courses_stmt->bind_param("sss", $selected_program, $selected_tech, $selected_year);
        $courses_stmt->execute();
        $courses_result = $courses_stmt->get_result();
        
        // Group courses by semester
        $available_courses = [
            'Semester 1' => [],
            'Semester 2' => []
        ];
        
        while ($course = $courses_result->fetch_assoc()) {
            $available_courses[$course['semester']][] = $course;
        }
    }
} elseif (isset($_GET['program']) && isset($_GET['tech']) && isset($_GET['year_level'])) {
    // Backward compatibility for GET requests
    $show_courses = true;
    $selected_program = $_GET['program'];
    $selected_tech = $_GET['tech'];
    $selected_year = $_GET['year_level'];
    
    // Fetch courses as before...
    $courses_sql = "SELECT course_id, course_name, semester FROM courses 
                   WHERE program = ? AND technology = ? AND year_level = ?
                   ORDER BY semester, course_id";
    $courses_stmt = $conn->prepare($courses_sql);
    $courses_stmt->bind_param("sss", $selected_program, $selected_tech, $selected_year);
    $courses_stmt->execute();
    $courses_result = $courses_stmt->get_result();
    
    $available_courses = [
        'Semester 1' => [],
        'Semester 2' => []
    ];
    
    while ($course = $courses_result->fetch_assoc()) {
        $available_courses[$course['semester']][] = $course;
    }
}

// Get province list for dropdowns
$provinces = [
    "Eastern Highlands Province",
    "East New Britain Province",
    "East Sepik Province",
    "Enga Province",
    "Gulf Province",
    "Hela Province",
    "Jiwaka Province",
    "Madang Province",
    "Manus Province",
    "Milne Bay Province",
    "Morobe Province",
    "National Capital District",
    "New Ireland Province",
    "Northern Province",
    "Autonomous Region of Bougainville",
    "Southern Highlands Province",
    "Western Province",
    "Western Highlands Province",
    "West New Britain Province",
    "West Sepik Province",
    "Chimbu Province"
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .registration-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .form-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        
        .form-section h3 {
            margin-top: 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            color: #333;
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        
        .form-group {
            flex: 1 0 calc(50% - 20px);
            margin: 10px;
        }
        
        @media (max-width: 768px) {
            .form-group {
                flex: 1 0 100%;
            }
            
            .registration-container {
                padding: 10px;
            }
            
            .form-section {
                padding: 15px;
            }
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        input[type="date"],
        select,
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        input:required, select:required {
            border-left: 3px solid #4CAF50;
        }
        
        input:focus:invalid {
            border-left: 3px solid #f44336;
        }
        
        .required {
            color: #f44336;
            margin-left: 3px;
        }
        
        .help-text {
            display: block;
            font-size: 12px;
            color: #666;
            margin-top: 3px;
        }
        
        button[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            width: 100%;
            max-width: 300px;
        }
        
        button[type="submit"]:hover {
            background-color: #45a049;
        }
        
        .course-selection {
            margin-top: 20px;
        }
        
        .semester-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        
        .course-checkbox {
            margin: 8px 0;
        }
        
        .course-checkbox label {
            display: flex;
            align-items: center;
            font-weight: normal;
        }
        
        .course-checkbox input {
            margin-right: 10px;
        }
        
        .step-indicator {
            display: flex;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .step {
            padding: 10px 15px;
            background-color: #eee;
            margin-right: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            flex: 1;
            text-align: center;
        }
        
        .step.active {
            background-color: #4CAF50;
            color: white;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeeba;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 4px;
            display: inline-block;
            margin-right: 10px;
        }
        
        .form-actions {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        @media (max-width: 576px) {
            .form-actions {
                flex-direction: column;
            }
            
            .btn-secondary, button[type="submit"] {
                width: 100%;
                text-align: center;
                margin-right: 0;
            }
        }
        
        /* Mobile Navigation Styles */
        .hamburger {
            display: none;
            cursor: pointer;
            padding: 10px;
            z-index: 1000;
        }

        .hamburger span {
            display: block;
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        @media screen and (max-width: 768px) {
            .hamburger {
                display: block;
            }

            .nav-links {
                display: none;
                width: 100%;
                position: absolute;
                top: 70px;
                left: 0;
                background-color: #ffbf00;
                flex-direction: column;
                align-items: center;
                padding: 20px 0;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }

            .nav-links.active {
                display: flex;
            }

            .nav-links li {
                margin: 10px 0;
                width: 100%;
                text-align: center;
            }

            .nav-links li a {
                width: 80%;
                margin: 0 auto;
                display: block;
            }

            /* Hamburger animation */
            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .hamburger.active span:nth-child(2) {
                opacity: 0;
            }

            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -7px);
            }

            /* Mobile form optimization */
            .form-row {
                flex-direction: column;
            }

            .form-group {
                width: 100%;
                margin: 10px 0;
            }

            .registration-container {
                padding: 10px;
            }

            .form-section {
                padding: 15px;
            }

            button[type="submit"] {
                width: 100%;
                max-width: none;
            }

            .step-indicator {
                flex-direction: column;
            }

            .step {
                width: 100%;
                margin-bottom: 10px;
            }

            /* Adjust logo and heading for mobile */
            nav .logo {
                display: flex;
                align-items: center;
                max-width: 70%;
            }

            nav .logo img {
                height: 40px;
                width: auto;
                margin-right: 10px;
            }

            nav .logo h2 {
                font-size: 16px;
                margin: 0;
            }
        }
        
        /* Error message styling */
        .error-message {
            color: #dc3545;
            margin-top: 5px;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <!-- Navigation Bar -->
    <nav>
        <div class="logo">
            <img src="img/logo.webp" alt="DBTI Logo">
            <h2>DBTI Online Registration</h2>
        </div>
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <ul class="nav-links">
            <li><a href="student_dashboard.php">Dashboard</a></li>
            <li><a href="registration.php" class="active">Registration</a></li>
            <li><a href="logout.php">Logout</a></li>
        </ul>
    </nav>
    
    <div class="registration-container">
        <h1>Student Registration</h1>
        
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger">
                <?php 
                    echo $_SESSION['error']; 
                    unset($_SESSION['error']);
                ?>
            </div>
        <?php endif; ?>
        
        <?php if(empty($programs)): ?>
            <div class="alert alert-danger">
                <strong>Error:</strong> No programs are available for registration. Please contact the registrar to have courses added to the system.
            </div>
        <?php else: ?>
            <div class="step-indicator">
                <div class="step <?php echo !$show_courses ? 'active' : ''; ?>">Step 1: Student Information & Program Selection</div>
                <div class="step <?php echo $show_courses ? 'active' : ''; ?>">Step 2: Course Selection</div>
            </div>
            
            <?php if (!$show_courses): ?>
                <!-- Step 1: Student Information Form -->
                <form action="registration.php" method="post">
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h3>Personal Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="student_id">Student ID:</label>
                                <input type="text" id="student_id" name="student_id" value="<?php echo htmlspecialchars($student['student_id']); ?>" readonly>
                            </div>
                            <div class="form-group">
                                <label for="first_name">First Name:</label>
                                <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($student['first_name']); ?>" readonly>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="last_name">Last Name:</label>
                                <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($student['last_name']); ?>" readonly>
                            </div>
                            <div class="form-group">
                                <label for="gender">Gender:<span class="required">*</span></label>
                                <select id="gender" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="Male" <?php echo ($student['gender'] == 'Male') ? 'selected' : ''; ?>>Male</option>
                                    <option value="Female" <?php echo ($student['gender'] == 'Female') ? 'selected' : ''; ?>>Female</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="dob">Date of Birth:<span class="required">*</span></label>
                                <input type="date" id="dob" name="dob" value="<?php echo htmlspecialchars($student['dob']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="phone_number">Phone Number:<span class="required">*</span></label>
                                <input type="tel" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($student['phone_number']); ?>" required>
                                <span class="help-text">Format: XXX XXXX (e.g. 123 4567)</span>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="student_email">Email Address:<span class="required">*</span></label>
                                <input type="email" id="student_email" name="student_email" value="<?php echo htmlspecialchars($student['student_email']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="residential_address">Residential Address:<span class="required">*</span></label>
                                <input type="text" id="residential_address" name="residential_address" value="<?php echo htmlspecialchars($student['residential_address']); ?>" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="home_province">Home Province:<span class="required">*</span></label>
                                <select id="home_province" name="home_province" required>
                                    <option value="">Select Province</option>
                                    <?php foreach ($provinces as $province): ?>
                                        <option value="<?php echo htmlspecialchars($province); ?>" <?php echo ($student['home_province'] == $province) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($province); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Guardian Information Section -->
                    <div class="form-section">
                        <h3>Guardian Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="guardian_name">Guardian Name:<span class="required">*</span></label>
                                <input type="text" id="guardian_name" name="guardian_name" value="<?php echo htmlspecialchars($student['guardian_name']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="guardian_occupation">Guardian Occupation:<span class="required">*</span></label>
                                <input type="text" id="guardian_occupation" name="guardian_occupation" value="<?php echo htmlspecialchars($student['guardian_occupation']); ?>" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="guardian_phone_number">Guardian Phone Number:<span class="required">*</span></label>
                                <input type="tel" id="guardian_phone_number" name="guardian_phone_number" value="<?php echo htmlspecialchars($student['guardian_phone_number']); ?>" required>
                                <span class="help-text">Format: XXX XXXX (e.g. 123 4567)</span>
                            </div>
                            <div class="form-group">
                                <label for="guardian_email">Guardian Email:<span class="required">*</span></label>
                                <input type="email" id="guardian_email" name="guardian_email" value="<?php echo htmlspecialchars($student['guardian_email']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Program Selection Section -->
                    <div class="form-section">
                        <h3>Program Selection</h3>
                        <p class="info-text">Please select your program, technology, and year level:</p>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="program">Select Program:</label>
                                <select id="program" name="program" required>
                                    <option value="">Select Program</option>
                                    <?php 
                                    // Get programs from the database
                                    $program_query = "SELECT DISTINCT program FROM courses WHERE course_id NOT LIKE 'PLACEHOLDER%' AND course_id NOT LIKE 'TEMP%' ORDER BY program";
                                    $program_result = $conn->query($program_query);
                                    
                                    if ($program_result && $program_result->num_rows > 0) {
                                        while ($program_row = $program_result->fetch_assoc()) {
                                            $program_name = $program_row['program'];
                                            $selected = ($student['program'] == $program_name) ? 'selected' : '';
                                            echo "<option value=\"" . htmlspecialchars($program_name) . "\" $selected>" . htmlspecialchars($program_name) . "</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="tech">Technology:</label>
                                <select id="tech" name="tech" required onchange="loadYears()">
                                    <option value="">Select Technology</option>
                                    <!-- Options will be loaded via JavaScript -->
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="year_level">Year Level:</label>
                                <select id="year_level" name="year_level" required>
                                    <option value="">Select Year Level</option>
                                    <!-- Options will be loaded via JavaScript -->
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Add a hidden field to track the step -->
                    <input type="hidden" name="step" value="1">
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Continue to Course Selection</button>
                    </div>
                </form>
            <?php else: ?>
                <!-- Step 2: Course Selection Form -->
                <div class="course-selection">
                    <h3>Available Courses for <?php echo htmlspecialchars($selected_program); ?> - <?php echo htmlspecialchars($selected_tech); ?> - <?php echo htmlspecialchars($selected_year); ?></h3>
                    
                    <form action="process_registration.php" method="post" id="course-registration-form">
                        <!-- Include all student information as hidden fields -->
                        <input type="hidden" name="student_id" value="<?php echo htmlspecialchars(isset($_POST['student_id']) ? $_POST['student_id'] : $student['student_id']); ?>">
                        <input type="hidden" name="first_name" value="<?php echo htmlspecialchars(isset($_POST['first_name']) ? $_POST['first_name'] : $student['first_name']); ?>">
                        <input type="hidden" name="last_name" value="<?php echo htmlspecialchars(isset($_POST['last_name']) ? $_POST['last_name'] : $student['last_name']); ?>">
                        <input type="hidden" name="gender" value="<?php echo htmlspecialchars(isset($_POST['gender']) ? $_POST['gender'] : $student['gender']); ?>">
                        <input type="hidden" name="dob" value="<?php echo htmlspecialchars(isset($_POST['dob']) ? $_POST['dob'] : $student['dob']); ?>">
                        <input type="hidden" name="phone_number" value="<?php echo htmlspecialchars(isset($_POST['phone_number']) ? $_POST['phone_number'] : $student['phone_number']); ?>">
                        <input type="hidden" name="student_email" value="<?php echo htmlspecialchars(isset($_POST['student_email']) ? $_POST['student_email'] : $student['student_email']); ?>">
                        <input type="hidden" name="residential_address" value="<?php echo htmlspecialchars(isset($_POST['residential_address']) ? $_POST['residential_address'] : $student['residential_address']); ?>">
                        <input type="hidden" name="home_province" value="<?php echo htmlspecialchars(isset($_POST['home_province']) ? $_POST['home_province'] : $student['home_province']); ?>">
                        <input type="hidden" name="guardian_name" value="<?php echo htmlspecialchars(isset($_POST['guardian_name']) ? $_POST['guardian_name'] : $student['guardian_name']); ?>">
                        <input type="hidden" name="guardian_occupation" value="<?php echo htmlspecialchars(isset($_POST['guardian_occupation']) ? $_POST['guardian_occupation'] : $student['guardian_occupation']); ?>">
                        <input type="hidden" name="guardian_phone_number" value="<?php echo htmlspecialchars(isset($_POST['guardian_phone_number']) ? $_POST['guardian_phone_number'] : $student['guardian_phone_number']); ?>">
                        <input type="hidden" name="guardian_email" value="<?php echo htmlspecialchars(isset($_POST['guardian_email']) ? $_POST['guardian_email'] : $student['guardian_email']); ?>">
                        
                        <!-- Program Information -->
                        <input type="hidden" name="program" value="<?php echo htmlspecialchars($selected_program); ?>">
                        <input type="hidden" name="tech" value="<?php echo htmlspecialchars($selected_tech); ?>">
                        <input type="hidden" name="year_level" value="<?php echo htmlspecialchars($selected_year); ?>">

                        <?php if (empty($available_courses['Semester 1']) && empty($available_courses['Semester 2'])): ?>
                            <div class="alert alert-warning">
                                No courses found for this selection. Please contact the registrar.
                            </div>
                            <div class="form-actions">
                                <a href="registration.php" class="btn-secondary">Back to Step 1</a>
                            </div>
                        <?php else: ?>
                            <!-- Debug information (hidden in production) -->
                            <div style="font-size: 10px; color: #999; margin-bottom: 15px;">
                                <?php 
                                $course_count = count($available_courses['Semester 1']) + count($available_courses['Semester 2']);
                                echo "Found {$course_count} courses for this selection.";
                                ?>
                            </div>
                            
                            <!-- Semester 1 Courses -->
                            <div class="semester-section">
                                <h4>Semester 1 Courses</h4>
                                <?php if (empty($available_courses['Semester 1'])): ?>
                                    <p>No courses available for Semester 1.</p>
                                <?php else: ?>
                                    <?php foreach ($available_courses['Semester 1'] as $course): ?>
                                        <div class="course-checkbox">
                                            <label>
                                                <input type="checkbox" name="courses[]" value="<?php echo htmlspecialchars($course['course_id']); ?>" checked>
                                                <?php echo htmlspecialchars($course['course_id'] . ' - ' . $course['course_name']); ?>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Semester 2 Courses -->
                            <div class="semester-section">
                                <h4>Semester 2 Courses</h4>
                                <?php if (empty($available_courses['Semester 2'])): ?>
                                    <p>No courses available for Semester 2.</p>
                                <?php else: ?>
                                    <?php foreach ($available_courses['Semester 2'] as $course): ?>
                                        <div class="course-checkbox">
                                            <label>
                                                <input type="checkbox" name="courses[]" value="<?php echo htmlspecialchars($course['course_id']); ?>" checked>
                                                <?php echo htmlspecialchars($course['course_id'] . ' - ' . $course['course_name']); ?>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Ensure at least one course is selected -->
                            <script>
                                function validateCourseSelection() {
                                    var checkboxes = document.querySelectorAll('input[name="courses[]"]:checked');
                                    
                                    if (checkboxes.length === 0) {
                                        alert('Please select at least one course to register for.');
                                        return false;
                                    }
                                    
                                    // Create a loading indicator
                                    const submitButton = document.getElementById('submit-button');
                                    submitButton.disabled = true;
                                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                                    
                                    // Create a submission status indicator
                                    const statusDiv = document.createElement('div');
                                    statusDiv.id = 'submission-status';
                                    statusDiv.style.position = 'fixed';
                                    statusDiv.style.bottom = '20px';
                                    statusDiv.style.right = '20px';
                                    statusDiv.style.padding = '15px';
                                    statusDiv.style.background = '#4CAF50';
                                    statusDiv.style.color = 'white';
                                    statusDiv.style.borderRadius = '5px';
                                    statusDiv.style.zIndex = '9999';
                                    statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting registration...';
                                    document.body.appendChild(statusDiv);
                                    
                                    // Submit the form
                                    document.getElementById('course-registration-form').submit();
                                    
                                    return false; // Prevent default form submission
                                }
                            </script>
                            
                            <div class="form-actions">
                                <a href="registration.php" class="btn-secondary">Back to Step 1</a>
                                <button type="button" id="submit-button" class="btn-primary" onclick="validateCourseSelection()">Complete Registration</button>
                            </div>
                        <?php endif; ?>
                    </form>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <footer>
        <p>&copy; <?php echo date("Y"); ?> Don Bosco Technological Institute. All Rights Reserved.</p>
    </footer>
    
    <script>
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const navLinks = document.querySelector('.nav-links');
            
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');
        }

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const hamburger = document.querySelector('.hamburger');
            const navLinks = document.querySelector('.nav-links');
            
            if (!hamburger.contains(event.target) && !navLinks.contains(event.target)) {
                hamburger.classList.remove('active');
                navLinks.classList.remove('active');
            }
        });

        // Close menu when clicking a link
        document.querySelectorAll('.nav-links a').forEach(link => {
            link.addEventListener('click', () => {
                const hamburger = document.querySelector('.hamburger');
                const navLinks = document.querySelector('.nav-links');
                
                hamburger.classList.remove('active');
                navLinks.classList.remove('active');
            });
        });

        // Prevent form zoom on mobile devices
        document.addEventListener('DOMContentLoaded', function() {
            const metaViewport = document.querySelector('meta[name=viewport]');
            metaViewport.content = 'width=device-width, initial-scale=1, maximum-scale=1';
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Phone number validation
            const phoneFields = document.querySelectorAll('input[type="tel"]');
            phoneFields.forEach(function(field) {
                field.addEventListener('input', function() {
                    // Remove any non-digits
                    let cleaned = this.value.replace(/\D/g, '');
                    
                    // Format as XXX XXXX if it has enough digits
                    if (cleaned.length >= 3) {
                        let formatted = cleaned.substring(0, 3);
                        if (cleaned.length > 3) {
                            formatted += ' ' + cleaned.substring(3, 10);
                        }
                        this.value = formatted;
                    } else {
                        this.value = cleaned;
                        }
                    });
                });
            
            // Course registration form enhancement
            const courseRegistrationForm = document.getElementById('course-registration-form');
            if (courseRegistrationForm) {
                courseRegistrationForm.addEventListener('submit', function(event) {
                    // Just before submission, double-check that all hidden fields have values
                    const hiddenFields = this.querySelectorAll('input[type="hidden"]');
                    let missingFields = [];
                    
                    hiddenFields.forEach(function(field) {
                        if (!field.value && field.name !== 'courses[]') {
                            console.log('Empty field found:', field.name);
                            // Try to find the value from the previous form or student data
                            if (field.name === 'phone_number' || field.name === 'guardian_phone_number') {
                                // Special handling for phone numbers
                                field.value = 'Autofilled'; // Just a placeholder to pass validation
                            }
                            
                            // If still empty, add to missing fields
                            if (!field.value) {
                                missingFields.push(field.name.replace('_', ' '));
                            }
                        }
                    });
                    
                    // Make sure at least one course is selected
                    const courseCheckboxes = this.querySelectorAll('input[name="courses[]"]:checked');
                    if (courseCheckboxes.length === 0) {
                        alert('Please select at least one course to register for.');
                        event.preventDefault();
                        return false;
                    }
                    
                    // Create a debug element to show submission
                    const debugDiv = document.createElement('div');
                    debugDiv.style.position = 'fixed';
                    debugDiv.style.bottom = '10px';
                    debugDiv.style.right = '10px';
                    debugDiv.style.background = '#4CAF50';
                    debugDiv.style.color = 'white';
                    debugDiv.style.padding = '10px';
                    debugDiv.style.borderRadius = '5px';
                    debugDiv.textContent = 'Submitting registration...';
                    document.body.appendChild(debugDiv);
                    
                    // Let the form submit
                    return true;
                });
            }
            
            // Form validation for Step 1
            const personalInfoForm = document.querySelector('form[action="registration.php"]');
            if (personalInfoForm) {
                personalInfoForm.addEventListener('submit', function(event) {
                    let hasErrors = false;
                    
                    // Validate phone numbers
                    const phoneInputs = this.querySelectorAll('input[type="tel"]');
                    phoneInputs.forEach(function(input) {
                        const digitsOnly = input.value.replace(/\D/g, '');
                        if (digitsOnly.length < 7) {
                            alert('Please enter a valid phone number for: ' + input.name.replace('_', ' '));
                            input.focus();
                            hasErrors = true;
                        }
                    });
                    
                    if (hasErrors) {
                        event.preventDefault();
                    }
                });
            }
            
            // Load technologies immediately when the page loads
            const programSelect = document.getElementById('program');
            if (programSelect) {
                // Trigger the loadTechnologies function immediately
                loadTechnologies();
                
                // Also add listener for changes
                programSelect.addEventListener('change', loadTechnologies);
            }
        });
        
        // Functions to load dropdown options
        function loadTechnologies(callback) {
            const program = document.getElementById('program').value;
            const techSelect = document.getElementById('tech');
            
            if (!techSelect) {
                console.error("Tech select element not found!");
                return;
            }
            
            techSelect.innerHTML = '<option value="">Select Technology</option>';
            
            if (!program) {
                console.log("No program selected");
                return;
            }
            
            console.log("Loading technologies for program:", program);
            
            // Add loading indicator
            techSelect.innerHTML = '<option value="">Loading...</option>';
            
            // Use direct AJAX request for better compatibility
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'get_technologies.php?program=' + encodeURIComponent(program), true);
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                    
                    techSelect.innerHTML = '<option value="">Select Technology</option>';
                    
                    if (!Array.isArray(data) || data.length === 0) {
                        techSelect.innerHTML = '<option value="">No technologies found</option>';
                        console.log("No technologies found for program:", program);
                        return;
                    }
                    
                    data.forEach(tech => {
                        const option = document.createElement('option');
                        option.value = tech;
                        option.textContent = tech;
                        techSelect.appendChild(option);
                    });
                        
                        console.log("Technologies loaded successfully:", data);
                    
                    if (typeof callback === 'function') {
                        callback();
                    }
                    } catch (e) {
                        console.error("Error parsing JSON:", e);
                    techSelect.innerHTML = '<option value="">Error loading technologies</option>';
                        
                        // Add error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-message';
                        errorDiv.textContent = 'Error parsing data. The registrar may need to add courses first.';
                    techSelect.parentNode.appendChild(errorDiv);
                    }
                } else {
                    console.error("Error loading technologies. Status:", xhr.status);
                    techSelect.innerHTML = '<option value="">Error loading technologies</option>';
                    
                    // Add error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-message';
                    errorDiv.textContent = 'Error: Server returned status ' + xhr.status + '. The registrar may need to add courses first.';
                    techSelect.parentNode.appendChild(errorDiv);
                }
            };
            
            xhr.onerror = function() {
                console.error("Network error when loading technologies");
                techSelect.innerHTML = '<option value="">Error loading technologies</option>';
                
                // Add error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = 'Network error. Please check your connection.';
                techSelect.parentNode.appendChild(errorDiv);
            };
            
            xhr.send();
        }
        
        function loadYears(callback) {
            const program = document.getElementById('program').value;
            const tech = document.getElementById('tech').value;
            const yearSelect = document.getElementById('year_level');
            
            if (!yearSelect) {
                console.error("Year select element not found!");
                return;
            }
            
            yearSelect.innerHTML = '<option value="">Select Year</option>';
            
            if (!program || !tech) {
                console.log("Program or tech not selected");
                return;
            }
            
            console.log("Loading years for program:", program, "tech:", tech);
            
            // Add loading indicator
            yearSelect.innerHTML = '<option value="">Loading...</option>';
            
            // Use direct AJAX request for better compatibility
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'get_years.php?program=' + encodeURIComponent(program) + '&technology=' + encodeURIComponent(tech), true);
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const years = JSON.parse(xhr.responseText);
                        
                        yearSelect.innerHTML = '<option value="">Select Year Level</option>';
                    if (!Array.isArray(years) || years.length === 0) {
                        yearSelect.innerHTML = '<option value="">No years found</option>';
                            console.log("No years found for program:", program, "tech:", tech);
                        return;
                    }
                        
                    years.forEach(year => {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = year;
                        yearSelect.appendChild(option);
                    });
                        
                        console.log("Years loaded successfully:", years);
                    
                    if (typeof callback === 'function') {
                        callback();
                    }
                    } catch (e) {
                        console.error("Error parsing JSON:", e);
                    yearSelect.innerHTML = '<option value="">Error loading years</option>';
                        
                        // Add error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-message';
                        errorDiv.textContent = 'Error parsing data.';
                    yearSelect.parentNode.appendChild(errorDiv);
                    }
                } else {
                    console.error("Error loading years. Status:", xhr.status);
                    yearSelect.innerHTML = '<option value="">Error loading years</option>';
                    
                    // Add error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-message';
                    errorDiv.textContent = 'Error: Server returned status ' + xhr.status;
                    yearSelect.parentNode.appendChild(errorDiv);
                }
            };
            
            xhr.onerror = function() {
                console.error("Network error when loading years");
                yearSelect.innerHTML = '<option value="">Error loading years</option>';
                
                // Add error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = 'Network error. Please check your connection.';
                yearSelect.parentNode.appendChild(errorDiv);
            };
            
            xhr.send();
        }
        
        // Add change listeners after defining the functions
        window.addEventListener('DOMContentLoaded', function() {
            const techSelect = document.getElementById('tech');
            if (techSelect) {
                techSelect.addEventListener('change', function() {
                    console.log("Technology changed to:", this.value);
                    loadYears();
                });
            }
            
            // Also trigger loadTechnologies if program is already selected
            const programSelect = document.getElementById('program');
            if (programSelect && programSelect.value) {
                loadTechnologies();
            }
        });
    </script>
</body>
</html>
