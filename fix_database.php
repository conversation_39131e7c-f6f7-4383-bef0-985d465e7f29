<?php
// <PERSON><PERSON><PERSON> to fix database structure
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'db_conn.php';

echo "<h1>Database Structure Repair</h1>";

// Check and fix courses table
$sql = "CREATE TABLE IF NOT EXISTS courses (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            course_id VARCHAR(20) NOT NULL,
            course_name VARCHAR(255) NOT NULL,
            program VARCHAR(100) NOT NULL,
            technology VARCHAR(100) NOT NULL,
            year_level VARCHAR(50) NOT NULL,
            semester VARCHAR(20) NOT NULL DEFAULT 'Semester 1',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

if ($conn->query($sql)) {
    echo "<p>✅ Courses table verified/created successfully!</p>";
} else {
    echo "<p>❌ Error with courses table: " . $conn->error . "</p>";
}

// Check and fix students table
$sql = "CREATE TABLE IF NOT EXISTS students (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id VARCHAR(20) NOT NULL UNIQUE,
            username VARCHAR(50) NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            gender VARCHAR(10),
            dob DATE,
            phone_number VARCHAR(20),
            student_email VARCHAR(100),
            residential_address TEXT,
            home_province VARCHAR(100),
            guardian_name VARCHAR(100),
            guardian_occupation VARCHAR(100),
            guardian_phone_number VARCHAR(20),
            guardian_email VARCHAR(100),
            program VARCHAR(100),
            tech VARCHAR(100),
            year_level VARCHAR(50),
            registration_status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

if ($conn->query($sql)) {
    echo "<p>✅ Students table verified/created successfully!</p>";
} else {
    echo "<p>❌ Error with students table: " . $conn->error . "</p>";
}

// Check and fix registrations table
$sql = "CREATE TABLE IF NOT EXISTS registrations (
            registration_id INT AUTO_INCREMENT PRIMARY KEY,
            student_id VARCHAR(20) NOT NULL,
            registration_date DATETIME NOT NULL,
            semester VARCHAR(20) NOT NULL,
            academic_year VARCHAR(10) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'current',
            CONSTRAINT fk_student FOREIGN KEY (student_id) REFERENCES students(student_id)
                ON DELETE CASCADE ON UPDATE CASCADE
        )";

if ($conn->query($sql)) {
    echo "<p>✅ Registrations table verified/created successfully!</p>";
} else {
    echo "<p>❌ Error with registrations table: " . $conn->error . "</p>";
    // If there's an error, it might be due to missing foreign key
    if (strpos($conn->error, 'foreign key constraint') !== false) {
        echo "<p>Attempting to create registrations table without foreign key...</p>";
        $sql = "CREATE TABLE IF NOT EXISTS registrations (
                    registration_id INT AUTO_INCREMENT PRIMARY KEY,
                    student_id VARCHAR(20) NOT NULL,
                    registration_date DATETIME NOT NULL,
                    semester VARCHAR(20) NOT NULL,
                    academic_year VARCHAR(10) NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'current'
                )";
        
        if ($conn->query($sql)) {
            echo "<p>✅ Registrations table created without foreign key constraint.</p>";
        } else {
            echo "<p>❌ Error creating registrations table: " . $conn->error . "</p>";
        }
    }
}

// Check and fix course_registrations table
$sql = "CREATE TABLE IF NOT EXISTS course_registrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            registration_id INT NOT NULL,
            course_id VARCHAR(20) NOT NULL,
            CONSTRAINT fk_registration FOREIGN KEY (registration_id) REFERENCES registrations(registration_id)
                ON DELETE CASCADE ON UPDATE CASCADE
        )";

if ($conn->query($sql)) {
    echo "<p>✅ Course registrations table verified/created successfully!</p>";
} else {
    echo "<p>❌ Error with course registrations table: " . $conn->error . "</p>";
    // If there's an error, it might be due to missing foreign key
    if (strpos($conn->error, 'foreign key constraint') !== false) {
        echo "<p>Attempting to create course_registrations table without foreign key...</p>";
        $sql = "CREATE TABLE IF NOT EXISTS course_registrations (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    registration_id INT NOT NULL,
                    course_id VARCHAR(20) NOT NULL
                )";
        
        if ($conn->query($sql)) {
            echo "<p>✅ Course registrations table created without foreign key constraint.</p>";
        } else {
            echo "<p>❌ Error creating course_registrations table: " . $conn->error . "</p>";
        }
    }
}

// Check and fix payments table
$sql = "CREATE TABLE IF NOT EXISTS payments (
            payment_id INT AUTO_INCREMENT PRIMARY KEY,
            student_id VARCHAR(20) NOT NULL,
            amount DECIMAL(10, 2) NOT NULL,
            payment_date DATETIME NOT NULL,
            payment_method VARCHAR(50) NOT NULL,
            receipt_number VARCHAR(50),
            payment_status VARCHAR(20) DEFAULT 'completed',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

if ($conn->query($sql)) {
    echo "<p>✅ Payments table verified/created successfully!</p>";
} else {
    echo "<p>❌ Error with payments table: " . $conn->error . "</p>";
}

// Database structure verification
echo "<h2>Database Structure Verification</h2>";

// Check for courses with missing year levels
$sql = "SELECT COUNT(*) as count FROM courses WHERE year_level = '' OR year_level IS NULL";
$result = $conn->query($sql);
$row = $result->fetch_assoc();
echo "<p>Courses with missing year levels: " . $row['count'] . "</p>";

// Check for students with registration status
$sql = "SELECT registration_status, COUNT(*) as count FROM students GROUP BY registration_status";
$result = $conn->query($sql);
echo "<p>Student registration status breakdown:</p><ul>";
while ($row = $result->fetch_assoc()) {
    echo "<li>" . $row['registration_status'] . ": " . $row['count'] . "</li>";
}
echo "</ul>";

// Check if any registrations exist
$sql = "SELECT COUNT(*) as count FROM registrations";
if ($result = $conn->query($sql)) {
    $row = $result->fetch_assoc();
    echo "<p>Total registrations: " . $row['count'] . "</p>";
} else {
    echo "<p>Error checking registrations: " . $conn->error . "</p>";
}

// Check if any course registrations exist
$sql = "SELECT COUNT(*) as count FROM course_registrations";
if ($result = $conn->query($sql)) {
    $row = $result->fetch_assoc();
    echo "<p>Total course registrations: " . $row['count'] . "</p>";
} else {
    echo "<p>Error checking course registrations: " . $conn->error . "</p>";
}

echo "<p><a href='admin_dashboard.php'>Return to Dashboard</a></p>";
?> 